(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{2573:(e,o,s)=>{"use strict";s.d(o,{$:()=>t});var r=s(5155);s(2115);let t=e=>{let{variant:o="primary",size:s="md",className:t,children:n,...i}=e;return(0,r.jsx)("button",{className:function(){for(var e=arguments.length,o=Array(e),s=0;s<e;s++)o[s]=arguments[s];return o.filter(<PERSON><PERSON>an).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[o],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[s],t),...i,children:n})}},4406:(e,o,s)=>{Promise.resolve().then(s.bind(s,6616))},6616:(e,o,s)=>{"use strict";s.r(o),s.d(o,{default:()=>l});var r=s(5155),t=s(2115),n=s(6874),i=s.n(n),a=s(2573);function l(){let[e,o]=(0,t.useState)({name:"",email:"",password:"",confirmPassword:"",company:"",phone:"",role:"SHIPPER"}),[s,n]=(0,t.useState)(!1),l=async o=>{o.preventDefault(),n(!0),console.log("Registration attempt:",e),setTimeout(()=>{n(!1)},1e3)},d=e=>{let{name:s,value:r}=e.target;o(e=>({...e,[s]:r}))};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Cr\xe9er votre compte SABLOGISTIQUE"}),(0,r.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Ou"," ",(0,r.jsx)(i(),{href:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"connectez-vous \xe0 votre compte existant"})]})]}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:l,children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Type de compte"}),(0,r.jsxs)("select",{id:"role",name:"role",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",value:e.role,onChange:d,children:[(0,r.jsx)("option",{value:"SHIPPER",children:"Affr\xe9teur (Exp\xe9diteur)"}),(0,r.jsx)("option",{value:"CARRIER",children:"Transporteur"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Nom complet"}),(0,r.jsx)("input",{id:"name",name:"name",type:"text",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Votre nom complet",value:e.name,onChange:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Adresse email"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>",value:e.email,onChange:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700",children:"Entreprise"}),(0,r.jsx)("input",{id:"company",name:"company",type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nom de votre entreprise",value:e.company,onChange:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"T\xe9l\xe9phone"}),(0,r.jsx)("input",{id:"phone",name:"phone",type:"tel",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"+33 1 23 45 67 89",value:e.phone,onChange:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Mot de passe"}),(0,r.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Mot de passe",value:e.password,onChange:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmer le mot de passe"}),(0,r.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Confirmer le mot de passe",value:e.confirmPassword,onChange:d})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),(0,r.jsxs)("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-900",children:["J'accepte les"," ",(0,r.jsx)(i(),{href:"/terms",className:"text-indigo-600 hover:text-indigo-500",children:"conditions d'utilisation"})," ","et la"," ",(0,r.jsx)(i(),{href:"/privacy",className:"text-indigo-600 hover:text-indigo-500",children:"politique de confidentialit\xe9"})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)(a.$,{type:"submit",className:"group relative w-full",disabled:s,children:s?"Cr\xe9ation du compte...":"Cr\xe9er mon compte"})})]})]})})}}},e=>{var o=o=>e(e.s=o);e.O(0,[874,441,684,358],()=>o(4406)),_N_E=e.O()}]);