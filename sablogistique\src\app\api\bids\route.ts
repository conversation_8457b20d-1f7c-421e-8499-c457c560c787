import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schéma de validation pour la création d'enchère
const createBidSchema = z.object({
  amount: z.number().positive(),
  currency: z.string().default('EUR'),
  proposedPickupDate: z.string().datetime(),
  proposedDeliveryDate: z.string().datetime(),
  message: z.string().optional(),
  freightOfferId: z.string(),
  carrierId: z.string(),
})

// Schéma pour la mise à jour du statut
const updateBidStatusSchema = z.object({
  status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED', 'WITHDRAWN']),
})

// GET /api/bids - Récupérer les enchères
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const freightOfferId = searchParams.get('freightOfferId')
    const carrierId = searchParams.get('carrierId')
    const status = searchParams.get('status')

    const bids = await prisma.bid.findMany({
      where: {
        ...(freightOfferId && { freightOfferId }),
        ...(carrierId && { carrierId }),
        ...(status && { status: status as any }),
      },
      include: {
        freightOffer: {
          include: {
            origin: true,
            destination: true,
            shipper: {
              select: {
                id: true,
                name: true,
                company: true,
              }
            }
          }
        },
        carrier: {
          select: {
            id: true,
            name: true,
            company: true,
            phone: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(bids)
  } catch (error) {
    console.error('Erreur lors de la récupération des enchères:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

// POST /api/bids - Créer une nouvelle enchère
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createBidSchema.parse(body)

    // Vérifier que l'offre existe et est active
    const freightOffer = await prisma.freightOffer.findUnique({
      where: { id: validatedData.freightOfferId }
    })

    if (!freightOffer) {
      return NextResponse.json(
        { error: 'Offre de fret non trouvée' },
        { status: 404 }
      )
    }

    if (freightOffer.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'Cette offre n\'est plus active' },
        { status: 400 }
      )
    }

    // Vérifier que le transporteur n'a pas déjà fait une offre
    const existingBid = await prisma.bid.findFirst({
      where: {
        freightOfferId: validatedData.freightOfferId,
        carrierId: validatedData.carrierId,
        status: { not: 'WITHDRAWN' }
      }
    })

    if (existingBid) {
      return NextResponse.json(
        { error: 'Vous avez déjà soumis une offre pour ce transport' },
        { status: 400 }
      )
    }

    const bid = await prisma.bid.create({
      data: {
        amount: validatedData.amount,
        currency: validatedData.currency,
        proposedPickupDate: new Date(validatedData.proposedPickupDate),
        proposedDeliveryDate: new Date(validatedData.proposedDeliveryDate),
        message: validatedData.message,
        freightOfferId: validatedData.freightOfferId,
        carrierId: validatedData.carrierId,
      },
      include: {
        freightOffer: {
          include: {
            origin: true,
            destination: true,
          }
        },
        carrier: {
          select: {
            id: true,
            name: true,
            company: true,
          }
        }
      }
    })

    // Créer une notification pour l'affréteur
    await prisma.notification.create({
      data: {
        type: 'BID_RECEIVED',
        title: 'Nouvelle offre reçue',
        message: `Vous avez reçu une nouvelle offre de ${bid.carrier.company || bid.carrier.name} pour ${bid.freightOffer.title}`,
        userId: freightOffer.shipperId,
      }
    })

    return NextResponse.json(bid, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Erreur lors de la création de l\'enchère:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
