'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

// Mock data pour la démonstration
const mockOffers = [
  {
    id: '1',
    title: 'Transport Paris - Lyon',
    type: 'SPOT',
    origin: 'Paris, France',
    destination: 'Lyon, France',
    pickupDate: '2024-01-15T08:00:00',
    deliveryDate: '2024-01-15T18:00:00',
    weight: 2500,
    volume: 15.5,
    cargoType: 'Électronique',
    budget: 850,
    distance: 465,
    description: 'Transport de matériel électronique fragile. Manipulation avec précaution requise.',
    shipper: 'TechCorp SARL',
    bidsCount: 3,
    timeLeft: '2 jours',
  },
  {
    id: '2',
    title: 'Livraison Bordeaux - Nantes',
    type: 'TERM',
    origin: 'Bordeaux, France',
    destination: 'Nantes, France',
    pickupDate: '2024-01-18T09:00:00',
    deliveryDate: '2024-01-18T16:00:00',
    weight: 3200,
    volume: 22.0,
    cargoType: 'Alimentaire',
    budget: 720,
    distance: 347,
    description: 'Produits alimentaires frais. Transport réfrigéré nécessaire.',
    shipper: 'FreshFood Distribution',
    bidsCount: 5,
    timeLeft: '5 jours',
  },
  {
    id: '3',
    title: 'Transport international Marseille - Barcelone',
    type: 'INTERNATIONAL',
    origin: 'Marseille, France',
    destination: 'Barcelone, Espagne',
    pickupDate: '2024-01-20T10:00:00',
    deliveryDate: '2024-01-21T14:00:00',
    weight: 1800,
    volume: 12.0,
    cargoType: 'Textile',
    budget: 950,
    distance: 520,
    description: 'Vêtements de mode. Documents douaniers fournis.',
    shipper: 'Fashion Europe Ltd',
    bidsCount: 2,
    timeLeft: '1 semaine',
  },
];

export default function CarrierOffersPage() {
  const [selectedOffer, setSelectedOffer] = useState(mockOffers[0]);
  const [showBidModal, setShowBidModal] = useState(false);
  const [bidAmount, setBidAmount] = useState('');
  const [bidMessage, setBidMessage] = useState('');
  const [filters, setFilters] = useState({
    type: '',
    maxDistance: '',
    minBudget: '',
    cargoType: '',
  });

  const handleBidSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implémenter la logique d'enchère
    console.log('Bid submitted:', { offerId: selectedOffer.id, amount: bidAmount, message: bidMessage });
    setShowBidModal(false);
    setBidAmount('');
    setBidMessage('');
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'SPOT':
        return 'bg-red-100 text-red-800';
      case 'TERM':
        return 'bg-blue-100 text-blue-800';
      case 'INTERNATIONAL':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'SPOT':
        return 'Spot';
      case 'TERM':
        return 'À terme';
      case 'INTERNATIONAL':
        return 'International';
      default:
        return type;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                SABLOGISTIQUE
              </Link>
              <span className="ml-4 text-gray-500">Offres disponibles</span>
            </div>
            <nav className="flex space-x-8">
              <Link href="/carrier" className="text-gray-600 hover:text-indigo-600">
                Tableau de bord
              </Link>
              <Link href="/carrier/tracking" className="text-gray-600 hover:text-indigo-600">
                Mes transports
              </Link>
              <Button variant="outline" size="sm">
                Déconnexion
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Filters */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Filtres</h3>
              <div className="space-y-4">
                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                    Type d&apos;offre
                  </label>
                  <select
                    id="type"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={filters.type}
                    onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                  >
                    <option value="">Tous les types</option>
                    <option value="SPOT">Spot</option>
                    <option value="TERM">À terme</option>
                    <option value="INTERNATIONAL">International</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="cargoType" className="block text-sm font-medium text-gray-700">
                    Type de marchandise
                  </label>
                  <input
                    type="text"
                    id="cargoType"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Ex: Électronique"
                    value={filters.cargoType}
                    onChange={(e) => setFilters(prev => ({ ...prev, cargoType: e.target.value }))}
                  />
                </div>

                <div>
                  <label htmlFor="maxDistance" className="block text-sm font-medium text-gray-700">
                    Distance max (km)
                  </label>
                  <input
                    type="number"
                    id="maxDistance"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="500"
                    value={filters.maxDistance}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxDistance: e.target.value }))}
                  />
                </div>

                <div>
                  <label htmlFor="minBudget" className="block text-sm font-medium text-gray-700">
                    Budget min (€)
                  </label>
                  <input
                    type="number"
                    id="minBudget"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="500"
                    value={filters.minBudget}
                    onChange={(e) => setFilters(prev => ({ ...prev, minBudget: e.target.value }))}
                  />
                </div>

                <Button className="w-full">
                  Appliquer les filtres
                </Button>
              </div>
            </div>

            {/* Offers List */}
            <div className="bg-white shadow rounded-lg mt-6">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Offres ({mockOffers.length})
                </h3>
                <div className="space-y-3">
                  {mockOffers.map((offer) => (
                    <div
                      key={offer.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedOffer.id === offer.id
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedOffer(offer)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <p className="text-sm font-medium text-gray-900 truncate">{offer.title}</p>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(offer.type)}`}>
                          {getTypeText(offer.type)}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 mb-1">{offer.origin} → {offer.destination}</p>
                      <p className="text-xs text-gray-600 mb-2">{offer.weight} kg • {offer.distance} km</p>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-bold text-green-600">{offer.budget} €</span>
                        <span className="text-xs text-gray-500">{offer.bidsCount} offres</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Offer Details */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-xl font-medium text-gray-900">{selectedOffer.title}</h3>
                    <p className="text-sm text-gray-500">Par {selectedOffer.shipper}</p>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getTypeColor(selectedOffer.type)}`}>
                      {getTypeText(selectedOffer.type)}
                    </span>
                    <p className="text-sm text-gray-500 mt-1">Expire dans {selectedOffer.timeLeft}</p>
                  </div>
                </div>

                {/* Offer Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Trajet</h4>
                    <p className="text-sm text-gray-600">{selectedOffer.origin}</p>
                    <p className="text-sm text-gray-600">↓</p>
                    <p className="text-sm text-gray-600">{selectedOffer.destination}</p>
                    <p className="text-sm text-gray-500 mt-1">{selectedOffer.distance} km</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Dates</h4>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Collecte:</span> {new Date(selectedOffer.pickupDate).toLocaleString('fr-FR')}
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Livraison:</span> {new Date(selectedOffer.deliveryDate).toLocaleString('fr-FR')}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Marchandise</h4>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Type:</span> {selectedOffer.cargoType}
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Poids:</span> {selectedOffer.weight.toLocaleString()} kg
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Volume:</span> {selectedOffer.volume} m³
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Budget</h4>
                    <p className="text-2xl font-bold text-green-600">{selectedOffer.budget} €</p>
                    <p className="text-sm text-gray-500">{selectedOffer.bidsCount} offres reçues</p>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-sm text-gray-600">{selectedOffer.description}</p>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <Button
                    onClick={() => setShowBidModal(true)}
                    className="flex-1"
                  >
                    Faire une offre
                  </Button>
                  <Button variant="outline">
                    Contacter l&apos;expéditeur
                  </Button>
                  <Button variant="outline">
                    Sauvegarder
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bid Modal */}
      {showBidModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Faire une offre pour: {selectedOffer.title}
              </h3>
              <form onSubmit={handleBidSubmit} className="space-y-4">
                <div>
                  <label htmlFor="bidAmount" className="block text-sm font-medium text-gray-700">
                    Montant de votre offre (€) *
                  </label>
                  <input
                    type="number"
                    id="bidAmount"
                    required
                    min="1"
                    step="0.01"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder={`Max: ${selectedOffer.budget}`}
                    value={bidAmount}
                    onChange={(e) => setBidAmount(e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-1">Budget maximum: {selectedOffer.budget} €</p>
                </div>

                <div>
                  <label htmlFor="bidMessage" className="block text-sm font-medium text-gray-700">
                    Message (optionnel)
                  </label>
                  <textarea
                    id="bidMessage"
                    rows={3}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Présentez votre offre, vos garanties, votre expérience..."
                    value={bidMessage}
                    onChange={(e) => setBidMessage(e.target.value)}
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowBidModal(false)}
                    className="flex-1"
                  >
                    Annuler
                  </Button>
                  <Button type="submit" className="flex-1">
                    Soumettre l&apos;offre
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
