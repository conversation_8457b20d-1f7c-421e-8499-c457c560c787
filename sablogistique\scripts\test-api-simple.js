const http = require('http');

console.log('🔍 Test simple de l\'API Users');
console.log('==============================\n');

function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/users',
    method: 'GET',
    timeout: 10000
  };

  console.log('📋 Test de l\'API Users...');
  console.log('🔗 URL: http://localhost:3000/api/users');

  const req = http.request(options, (res) => {
    console.log(`📊 Status Code: ${res.statusCode}`);
    console.log(`📋 Headers:`, res.headers);

    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('\n📄 Réponse reçue:');
      
      if (res.statusCode === 200) {
        try {
          const jsonData = JSON.parse(data);
          console.log('✅ Succès ! Données JSON valides');
          console.log(`📊 Nombre d'utilisateurs: ${jsonData.length}`);
          
          if (jsonData.length > 0) {
            console.log('\n👤 Premier utilisateur:');
            console.log(`- ID: ${jsonData[0].id}`);
            console.log(`- Nom: ${jsonData[0].name}`);
            console.log(`- Email: ${jsonData[0].email}`);
            console.log(`- Rôle: ${jsonData[0].role}`);
          }
        } catch (error) {
          console.log('❌ Erreur de parsing JSON:', error.message);
          console.log('📄 Données brutes:', data.substring(0, 200));
        }
      } else {
        console.log(`❌ Erreur HTTP ${res.statusCode}`);
        console.log('📄 Réponse:', data.substring(0, 500));
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ Erreur de connexion:', error.message);
    console.log('\n💡 Solutions possibles:');
    console.log('1. Vérifiez que le serveur Next.js est démarré');
    console.log('2. Vérifiez que le port 3000 est libre');
    console.log('3. Redémarrez le serveur: npm run dev');
  });

  req.on('timeout', () => {
    console.log('⏰ Timeout - Le serveur ne répond pas dans les temps');
    req.destroy();
  });

  req.end();
}

// Test immédiat
testAPI();

// Test après 2 secondes si le premier échoue
setTimeout(() => {
  console.log('\n🔄 Nouveau test après 2 secondes...');
  testAPI();
}, 2000);
