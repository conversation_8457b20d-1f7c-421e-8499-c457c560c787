(()=>{var e={};e.id=242,e.ids=[242],e.modules={440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},478:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\tracking\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\tracking\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3603:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(5239),i=t(8088),a=t(8170),n=t.n(a),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["tracking",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,478)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\tracking\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\tracking\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/tracking/page",pathname:"/tracking",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3852:()=>{},3873:e=>{"use strict";e.exports=require("path")},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>d});var r=t(7413),i=t(2376),a=t.n(i),n=t(8726),l=t.n(n);t(1135);let d={title:"Create Next App",description:"Generated by create next app"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${l().variable} antialiased`,children:e})})}},4494:(e,s,t)=>{Promise.resolve().then(t.bind(t,478))},4543:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},5060:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(687),i=t(3210),a=t(5814),n=t.n(a),l=t(7320);let d=[{id:"SH001",title:"Transport Paris - Lyon",status:"IN_TRANSIT",origin:"Paris, France",destination:"Lyon, France",currentLocation:"M\xe2con, France",progress:75,estimatedArrival:"2024-01-15T14:30:00",carrier:"Transport Express SARL",vehicle:"Camion 20T - AB-123-CD",events:[{id:"1",type:"PICKUP",description:"Marchandise collect\xe9e",location:"Paris, France",timestamp:"2024-01-14T08:00:00"},{id:"2",type:"IN_TRANSIT",description:"Transport en cours",location:"M\xe2con, France",timestamp:"2024-01-15T10:30:00"}]},{id:"SH002",title:"Livraison Marseille - Nice",status:"DELIVERED",origin:"Marseille, France",destination:"Nice, France",currentLocation:"Nice, France",progress:100,estimatedArrival:"2024-01-12T16:00:00",carrier:"Logistique M\xe9diterran\xe9e",vehicle:"Fourgon 3.5T - EF-456-GH",events:[{id:"1",type:"PICKUP",description:"Marchandise collect\xe9e",location:"Marseille, France",timestamp:"2024-01-12T09:00:00"},{id:"2",type:"DELIVERY",description:"Livraison effectu\xe9e",location:"Nice, France",timestamp:"2024-01-12T15:45:00"}]}];function o(){let[e,s]=(0,i.useState)(d[0]),[t,a]=(0,i.useState)(""),o=e=>{switch(e){case"PICKUP":return"bg-blue-100 text-blue-800";case"IN_TRANSIT":return"bg-yellow-100 text-yellow-800";case"DELIVERED":return"bg-green-100 text-green-800";case"DELAY":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case"PICKUP":return"Collecte";case"IN_TRANSIT":return"En transit";case"DELIVERED":return"Livr\xe9";case"DELAY":return"Retard";default:return e}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"SABLOGISTIQUE"}),(0,r.jsx)("span",{className:"ml-4 text-gray-500",children:"Suivi des exp\xe9ditions"})]}),(0,r.jsxs)("nav",{className:"flex space-x-8",children:[(0,r.jsx)(n(),{href:"/shipper",className:"text-gray-600 hover:text-indigo-600",children:"Tableau de bord"}),(0,r.jsx)(n(),{href:"/profile",className:"text-gray-600 hover:text-indigo-600",children:"Profil"}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"D\xe9connexion"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Rechercher une exp\xe9dition"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)("input",{type:"text",placeholder:"Entrez le code de suivi...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",value:t,onChange:e=>a(e.target.value)}),(0,r.jsx)(l.$,{children:"Rechercher"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Mes exp\xe9ditions"}),(0,r.jsx)("div",{className:"space-y-3",children:d.map(t=>(0,r.jsxs)("div",{className:`p-3 rounded-lg border cursor-pointer transition-colors ${e.id===t.id?"border-indigo-500 bg-indigo-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>s(t),children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t.id}),(0,r.jsx)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${o(t.status)}`,children:c(t.status)})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:t.title}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full",style:{width:`${t.progress}%`}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[t.progress,"% termin\xe9"]})]})]},t.id))})]})})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Code de suivi: ",e.id]})]}),(0,r.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${o(e.status)}`,children:c(e.status)})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,r.jsx)("span",{children:e.origin}),(0,r.jsx)("span",{children:e.destination})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,r.jsx)("div",{className:"bg-indigo-600 h-3 rounded-full transition-all duration-300",style:{width:`${e.progress}%`}})}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,r.jsx)("span",{children:"D\xe9part"}),(0,r.jsxs)("span",{children:[e.progress,"%"]}),(0,r.jsx)("span",{children:"Arriv\xe9e"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Position actuelle"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.currentLocation})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Arriv\xe9e estim\xe9e"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:new Date(e.estimatedArrival).toLocaleString("fr-FR")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Transporteur"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.carrier})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"V\xe9hicule"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.vehicle})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Carte de suivi"}),(0,r.jsx)("div",{className:"bg-gray-100 rounded-lg h-64 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Carte interactive disponible prochainement"})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-4",children:"Historique des \xe9v\xe9nements"}),(0,r.jsx)("div",{className:"flow-root",children:(0,r.jsx)("ul",{className:"-mb-8",children:e.events.map((s,t)=>(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"relative pb-8",children:[t!==e.events.length-1?(0,r.jsx)("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,r.jsxs)("div",{className:"relative flex space-x-3",children:[(0,r.jsx)("div",{children:(0,r.jsx)("span",{className:`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${o(s.type)}`,children:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})}),(0,r.jsxs)("div",{className:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-900",children:s.description}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:s.location})]}),(0,r.jsx)("div",{className:"text-right text-sm whitespace-nowrap text-gray-500",children:new Date(s.timestamp).toLocaleString("fr-FR")})]})]})]})},s.id))})})]})]})})})]})]})]})}},5708:()=>{},7320:(e,s,t)=>{"use strict";t.d(s,{$:()=>i});var r=t(687);t(3210);let i=({variant:e="primary",size:s="md",className:t,children:i,...a})=>(0,r.jsx)("button",{className:function(...e){return e.filter(Boolean).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[e],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[s],t),...a,children:i})},7542:(e,s,t)=>{Promise.resolve().then(t.bind(t,5060))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9815:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,145,567],()=>t(3603));module.exports=r})();