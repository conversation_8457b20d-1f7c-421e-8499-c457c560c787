/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/freight-offers/route";
exports.ids = ["app/api/freight-offers/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffreight-offers%2Froute&page=%2Fapi%2Ffreight-offers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffreight-offers%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffreight-offers%2Froute&page=%2Fapi%2Ffreight-offers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffreight-offers%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_xampp1_htdocs_SABLOGISTIQUE_sablogistique_src_app_api_freight_offers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/freight-offers/route.ts */ \"(rsc)/./src/app/api/freight-offers/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/freight-offers/route\",\n        pathname: \"/api/freight-offers\",\n        filename: \"route\",\n        bundlePath: \"app/api/freight-offers/route\"\n    },\n    resolvedPagePath: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\api\\\\freight-offers\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_xampp1_htdocs_SABLOGISTIQUE_sablogistique_src_app_api_freight_offers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffreight-offers%2Froute&page=%2Fapi%2Ffreight-offers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffreight-offers%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/freight-offers/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/freight-offers/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Schéma de validation pour la création d'offre\nconst createOfferSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    description: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    type: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        'SPOT',\n        'TERM',\n        'INTERNATIONAL'\n    ]),\n    cargoType: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1),\n    weight: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().positive(),\n    volume: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().positive().optional(),\n    quantity: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().positive(),\n    specialRequirements: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()).optional(),\n    pickupDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime(),\n    deliveryDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime().optional(),\n    budget: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().positive().optional(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().default('EUR'),\n    shipperId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n    origin: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        address: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        city: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        postalCode: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        country: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        latitude: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().optional(),\n        longitude: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().optional()\n    }),\n    destination: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        address: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        city: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        postalCode: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        country: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        latitude: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().optional(),\n        longitude: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().optional()\n    })\n});\n// GET /api/freight-offers - Récupérer les offres de fret\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get('type');\n        const status = searchParams.get('status');\n        const shipperId = searchParams.get('shipperId');\n        const carrierId = searchParams.get('carrierId');\n        const offers = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.freightOffer.findMany({\n            where: {\n                ...type && {\n                    type: type\n                },\n                ...status && {\n                    status: status\n                },\n                ...shipperId && {\n                    shipperId\n                },\n                ...carrierId && {\n                    carrierId\n                }\n            },\n            include: {\n                origin: true,\n                destination: true,\n                shipper: {\n                    select: {\n                        id: true,\n                        name: true,\n                        company: true\n                    }\n                },\n                carrier: {\n                    select: {\n                        id: true,\n                        name: true,\n                        company: true\n                    }\n                },\n                bids: {\n                    include: {\n                        carrier: {\n                            select: {\n                                id: true,\n                                name: true,\n                                company: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        amount: 'asc'\n                    }\n                },\n                _count: {\n                    select: {\n                        bids: true,\n                        trackingEvents: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(offers);\n    } catch (error) {\n        console.error('Erreur lors de la récupération des offres:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Erreur interne du serveur'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/freight-offers - Créer une nouvelle offre\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validatedData = createOfferSchema.parse(body);\n        // Créer les localisations d'origine et de destination\n        const origin = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.location.create({\n            data: validatedData.origin\n        });\n        const destination = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.location.create({\n            data: validatedData.destination\n        });\n        // Créer l'offre de fret\n        const offer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.freightOffer.create({\n            data: {\n                title: validatedData.title,\n                description: validatedData.description,\n                type: validatedData.type,\n                cargoType: validatedData.cargoType,\n                weight: validatedData.weight,\n                volume: validatedData.volume,\n                quantity: validatedData.quantity,\n                specialRequirements: validatedData.specialRequirements || [],\n                pickupDate: new Date(validatedData.pickupDate),\n                deliveryDate: validatedData.deliveryDate ? new Date(validatedData.deliveryDate) : null,\n                budget: validatedData.budget,\n                currency: validatedData.currency,\n                shipperId: validatedData.shipperId,\n                originId: origin.id,\n                destinationId: destination.id\n            },\n            include: {\n                origin: true,\n                destination: true,\n                shipper: {\n                    select: {\n                        id: true,\n                        name: true,\n                        company: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(offer, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Données invalides',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error('Erreur lors de la création de l\\'offre:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Erreur interne du serveur'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/freight-offers/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query',\n        'error',\n        'warn'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBSztRQUFDO1FBQVM7UUFBUztLQUFPO0FBQ2pDLEdBQUU7QUFFRixJQUFJQyxJQUFxQyxFQUFFSixnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFx4YW1wcDFcXGh0ZG9jc1xcU0FCTE9HSVNUSVFVRVxcc2FibG9naXN0aXF1ZVxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCh7XG4gIGxvZzogWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10sXG59KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffreight-offers%2Froute&page=%2Fapi%2Ffreight-offers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffreight-offers%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();