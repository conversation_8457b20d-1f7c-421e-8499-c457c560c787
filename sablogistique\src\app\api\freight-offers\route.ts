import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schéma de validation pour la création d'offre
const createOfferSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  type: z.enum(['SPOT', 'TERM', 'INTERNATIONAL']),
  cargoType: z.string().min(1),
  weight: z.number().positive(),
  volume: z.number().positive().optional(),
  quantity: z.number().positive(),
  specialRequirements: z.array(z.string()).optional(),
  pickupDate: z.string().datetime(),
  deliveryDate: z.string().datetime().optional(),
  budget: z.number().positive().optional(),
  currency: z.string().default('EUR'),
  shipperId: z.string(),
  origin: z.object({
    address: z.string(),
    city: z.string(),
    postalCode: z.string(),
    country: z.string(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
  }),
  destination: z.object({
    address: z.string(),
    city: z.string(),
    postalCode: z.string(),
    country: z.string(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
  }),
})

// GET /api/freight-offers - Récupérer les offres de fret
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const shipperId = searchParams.get('shipperId')
    const carrierId = searchParams.get('carrierId')

    const offers = await prisma.freightOffer.findMany({
      where: {
        ...(type && { type: type as any }),
        ...(status && { status: status as any }),
        ...(shipperId && { shipperId }),
        ...(carrierId && { carrierId }),
      },
      include: {
        origin: true,
        destination: true,
        shipper: {
          select: {
            id: true,
            name: true,
            company: true,
          }
        },
        carrier: {
          select: {
            id: true,
            name: true,
            company: true,
          }
        },
        bids: {
          include: {
            carrier: {
              select: {
                id: true,
                name: true,
                company: true,
              }
            }
          },
          orderBy: { amount: 'asc' }
        },
        _count: {
          select: {
            bids: true,
            trackingEvents: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(offers)
  } catch (error) {
    console.error('Erreur lors de la récupération des offres:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

// POST /api/freight-offers - Créer une nouvelle offre
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = createOfferSchema.parse(body)

    // Créer les localisations d'origine et de destination
    const origin = await prisma.location.create({
      data: validatedData.origin
    })

    const destination = await prisma.location.create({
      data: validatedData.destination
    })

    // Créer l'offre de fret
    const offer = await prisma.freightOffer.create({
      data: {
        title: validatedData.title,
        description: validatedData.description,
        type: validatedData.type,
        cargoType: validatedData.cargoType,
        weight: validatedData.weight,
        volume: validatedData.volume,
        quantity: validatedData.quantity,
        specialRequirements: validatedData.specialRequirements || [],
        pickupDate: new Date(validatedData.pickupDate),
        deliveryDate: validatedData.deliveryDate ? new Date(validatedData.deliveryDate) : null,
        budget: validatedData.budget,
        currency: validatedData.currency,
        shipperId: validatedData.shipperId,
        originId: origin.id,
        destinationId: destination.id,
      },
      include: {
        origin: true,
        destination: true,
        shipper: {
          select: {
            id: true,
            name: true,
            company: true,
          }
        }
      }
    })

    return NextResponse.json(offer, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Erreur lors de la création de l\'offre:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
