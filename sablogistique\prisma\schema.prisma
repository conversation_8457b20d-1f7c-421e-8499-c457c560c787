// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      Role
  company   String?
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  shippedOffers FreightOffer[] @relation("ShipperOffers")
  carriedOffers FreightOffer[] @relation("CarrierOffers")
  vehicles      Vehicle[]
  bids          Bid[]
  notifications Notification[]

  @@map("users")
}

model FreightOffer {
  id          String      @id @default(cuid())
  title       String
  description String?
  type        OfferType
  status      OfferStatus @default(ACTIVE)

  // Cargo details
  cargoType String
  weight    Int // in kg
  volume    Float? // in m³
  quantity  Int

  // Special requirements
  specialRequirements String[]

  // Locations
  originId      String
  destinationId String
  origin        Location @relation("OriginOffers", fields: [originId], references: [id])
  destination   Location @relation("DestinationOffers", fields: [destinationId], references: [id])

  // Dates
  pickupDate   DateTime
  deliveryDate DateTime?

  // Financial
  budget   Float?
  currency String @default("EUR")

  // Relations
  shipperId String
  carrierId String?
  shipper   User   @relation("ShipperOffers", fields: [shipperId], references: [id])
  carrier   User?  @relation("CarrierOffers", fields: [carrierId], references: [id])

  bids           Bid[]
  trackingEvents TrackingEvent[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("freight_offers")
}

model Location {
  id         String  @id @default(cuid())
  address    String
  city       String
  postalCode String
  country    String
  latitude   Float?
  longitude  Float?

  // Relations
  originOffers      FreightOffer[] @relation("OriginOffers")
  destinationOffers FreightOffer[] @relation("DestinationOffers")
  vehicles          Vehicle[]
  trackingEvents    TrackingEvent[]

  @@map("locations")
}

model Vehicle {
  id           String      @id @default(cuid())
  type         VehicleType
  licensePlate String      @unique
  capacity     Int // in kg
  volume       Float? // in m³
  isAvailable  Boolean     @default(true)

  // Current location
  currentLocationId String?
  currentLocation   Location? @relation(fields: [currentLocationId], references: [id])

  // Maintenance
  lastMaintenance DateTime?
  nextMaintenance DateTime?

  // Driver
  driver String?

  // Relations
  carrierId String
  carrier   User   @relation(fields: [carrierId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("vehicles")
}

model Bid {
  id                    String    @id @default(cuid())
  amount                Float
  currency              String    @default("EUR")
  proposedPickupDate    DateTime
  proposedDeliveryDate  DateTime
  message               String?
  status                BidStatus @default(PENDING)

  // Relations
  freightOfferId String
  carrierId      String
  freightOffer   FreightOffer @relation(fields: [freightOfferId], references: [id])
  carrier        User         @relation(fields: [carrierId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("bids")
}

model TrackingEvent {
  id          String           @id @default(cuid())
  type        TrackingEventType
  description String
  timestamp   DateTime         @default(now())
  metadata    Json?

  // Location
  locationId String?
  location   Location? @relation(fields: [locationId], references: [id])

  // Relations
  freightOfferId String
  freightOffer   FreightOffer @relation(fields: [freightOfferId], references: [id])

  @@map("tracking_events")
}

model Notification {
  id      String           @id @default(cuid())
  type    NotificationType
  title   String
  message String
  isRead  Boolean          @default(false)

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())

  @@map("notifications")
}

// Enums
enum Role {
  SHIPPER
  CARRIER
  ADMIN
}

enum OfferType {
  SPOT
  TERM
  INTERNATIONAL
}

enum OfferStatus {
  ACTIVE
  ASSIGNED
  COMPLETED
  CANCELLED
}

enum VehicleType {
  TRUCK
  VAN
  TRAILER
  CONTAINER
}

enum BidStatus {
  PENDING
  ACCEPTED
  REJECTED
  WITHDRAWN
}

enum TrackingEventType {
  PICKUP
  IN_TRANSIT
  DELIVERY
  DELAY
  INCIDENT
}

enum NotificationType {
  BID_RECEIVED
  BID_ACCEPTED
  BID_REJECTED
  TRACKING_UPDATE
  SYSTEM
}
