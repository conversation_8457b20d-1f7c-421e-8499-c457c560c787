'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

export default function NewOfferPage() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'SPOT' as 'SPOT' | 'TERM' | 'INTERNATIONAL',
    cargoType: '',
    weight: '',
    volume: '',
    quantity: '',
    originAddress: '',
    originCity: '',
    originPostalCode: '',
    originCountry: 'France',
    destinationAddress: '',
    destinationCity: '',
    destinationPostalCode: '',
    destinationCountry: 'France',
    pickupDate: '',
    deliveryDate: '',
    budget: '',
    currency: 'EUR',
    specialRequirements: [] as string[],
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // TODO: Implémenter la logique de création d'offre
    console.log('New offer:', formData);
    
    setTimeout(() => {
      setIsLoading(false);
      // Redirection vers le tableau de bord
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                SABLOGISTIQUE
              </Link>
              <span className="ml-4 text-gray-500">Nouvelle offre de fret</span>
            </div>
            <nav className="flex space-x-8">
              <Link href="/shipper" className="text-gray-600 hover:text-indigo-600">
                Tableau de bord
              </Link>
              <Button variant="outline" size="sm">
                Déconnexion
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-6">Publier une nouvelle offre de fret</h3>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Informations générales */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div className="sm:col-span-2">
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                    Titre de l&apos;offre *
                  </label>
                  <input
                    type="text"
                    name="title"
                    id="title"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Ex: Transport Paris - Lyon"
                    value={formData.title}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                    Type d&apos;offre *
                  </label>
                  <select
                    name="type"
                    id="type"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={formData.type}
                    onChange={handleInputChange}
                  >
                    <option value="SPOT">Spot (immédiat)</option>
                    <option value="TERM">À terme (planifié)</option>
                    <option value="INTERNATIONAL">International</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="cargoType" className="block text-sm font-medium text-gray-700">
                    Type de marchandise *
                  </label>
                  <input
                    type="text"
                    name="cargoType"
                    id="cargoType"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Ex: Électronique, Alimentaire, Textile..."
                    value={formData.cargoType}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label htmlFor="weight" className="block text-sm font-medium text-gray-700">
                    Poids (kg) *
                  </label>
                  <input
                    type="number"
                    name="weight"
                    id="weight"
                    required
                    min="1"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="2500"
                    value={formData.weight}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label htmlFor="volume" className="block text-sm font-medium text-gray-700">
                    Volume (m³)
                  </label>
                  <input
                    type="number"
                    name="volume"
                    id="volume"
                    step="0.1"
                    min="0"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="15.5"
                    value={formData.volume}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                    Quantité *
                  </label>
                  <input
                    type="number"
                    name="quantity"
                    id="quantity"
                    required
                    min="1"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="10"
                    value={formData.quantity}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Origine */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-4">Lieu de collecte</h4>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div className="sm:col-span-2">
                    <label htmlFor="originAddress" className="block text-sm font-medium text-gray-700">
                      Adresse *
                    </label>
                    <input
                      type="text"
                      name="originAddress"
                      id="originAddress"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="123 Rue de la République"
                      value={formData.originAddress}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div>
                    <label htmlFor="originCity" className="block text-sm font-medium text-gray-700">
                      Ville *
                    </label>
                    <input
                      type="text"
                      name="originCity"
                      id="originCity"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="Paris"
                      value={formData.originCity}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div>
                    <label htmlFor="originPostalCode" className="block text-sm font-medium text-gray-700">
                      Code postal *
                    </label>
                    <input
                      type="text"
                      name="originPostalCode"
                      id="originPostalCode"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="75001"
                      value={formData.originPostalCode}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </div>

              {/* Destination */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-4">Lieu de livraison</h4>
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div className="sm:col-span-2">
                    <label htmlFor="destinationAddress" className="block text-sm font-medium text-gray-700">
                      Adresse *
                    </label>
                    <input
                      type="text"
                      name="destinationAddress"
                      id="destinationAddress"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="456 Avenue de la Liberté"
                      value={formData.destinationAddress}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div>
                    <label htmlFor="destinationCity" className="block text-sm font-medium text-gray-700">
                      Ville *
                    </label>
                    <input
                      type="text"
                      name="destinationCity"
                      id="destinationCity"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="Lyon"
                      value={formData.destinationCity}
                      onChange={handleInputChange}
                    />
                  </div>

                  <div>
                    <label htmlFor="destinationPostalCode" className="block text-sm font-medium text-gray-700">
                      Code postal *
                    </label>
                    <input
                      type="text"
                      name="destinationPostalCode"
                      id="destinationPostalCode"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      placeholder="69001"
                      value={formData.destinationPostalCode}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              </div>

              {/* Dates et budget */}
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
                <div>
                  <label htmlFor="pickupDate" className="block text-sm font-medium text-gray-700">
                    Date de collecte *
                  </label>
                  <input
                    type="datetime-local"
                    name="pickupDate"
                    id="pickupDate"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={formData.pickupDate}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label htmlFor="deliveryDate" className="block text-sm font-medium text-gray-700">
                    Date de livraison souhaitée
                  </label>
                  <input
                    type="datetime-local"
                    name="deliveryDate"
                    id="deliveryDate"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={formData.deliveryDate}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label htmlFor="budget" className="block text-sm font-medium text-gray-700">
                    Budget maximum (€)
                  </label>
                  <input
                    type="number"
                    name="budget"
                    id="budget"
                    min="0"
                    step="0.01"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="850.00"
                    value={formData.budget}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description détaillée
                </label>
                <textarea
                  name="description"
                  id="description"
                  rows={4}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Décrivez votre marchandise, les conditions de transport, les exigences particulières..."
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                <Link href="/shipper">
                  <Button variant="outline">
                    Annuler
                  </Button>
                </Link>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? 'Publication...' : 'Publier l\'offre'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
