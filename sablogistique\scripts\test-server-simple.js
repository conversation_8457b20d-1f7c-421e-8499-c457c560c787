const http = require('http');

console.log('🔍 Test simple du serveur Next.js...\n');

function testServer() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/',
    method: 'GET',
    timeout: 5000
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Serveur répond !`);
    console.log(`📊 Status: ${res.statusCode}`);
    console.log(`🔗 URL: http://localhost:3000`);
    
    if (res.statusCode === 200) {
      console.log('\n🎉 L\'application fonctionne correctement !');
      console.log('🌐 Vous pouvez ouvrir: http://localhost:3000');
    } else {
      console.log(`\n⚠️  Status inattendu: ${res.statusCode}`);
    }
  });

  req.on('error', (err) => {
    console.log(`❌ Erreur de connexion: ${err.message}`);
    console.log('\n💡 Solutions possibles:');
    console.log('1. Vérifiez que le serveur est démarré: npm run dev');
    console.log('2. Vérifiez le port 3000');
    console.log('3. Redémarrez le serveur');
  });

  req.on('timeout', () => {
    console.log('⏰ Timeout - Le serveur ne répond pas');
    req.destroy();
  });

  req.end();
}

// Test immédiat
testServer();

// Test après 3 secondes (au cas où le serveur démarre lentement)
setTimeout(() => {
  console.log('\n🔄 Test après 3 secondes...');
  testServer();
}, 3000);
