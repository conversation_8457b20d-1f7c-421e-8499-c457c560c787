'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { MetricsCard } from '@/components/Dashboard/MetricsCard';
import { SimpleChart } from '@/components/Dashboard/SimpleChart';

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('30d');

  // Mock data pour la démonstration
  const metrics = {
    totalRevenue: 125420,
    totalShipments: 342,
    avgCost: 367,
    satisfaction: 4.8,
  };

  const revenueData = [
    { label: 'Jan', value: 12500 },
    { label: 'Fév', value: 15200 },
    { label: 'Mar', value: 18900 },
    { label: 'Avr', value: 16800 },
    { label: 'Mai', value: 21300 },
    { label: 'Jun', value: 19600 },
  ];

  const shipmentsByType = [
    { label: 'Spot', value: 156, color: '#EF4444' },
    { label: 'À terme', value: 124, color: '#3B82F6' },
    { label: 'International', value: 62, color: '#8B5CF6' },
  ];

  const topRoutes = [
    { route: 'Paris → Lyon', count: 45, revenue: 38250 },
    { route: 'Marseille → Toulouse', count: 32, revenue: 28800 },
    { route: 'Bordeaux → Nantes', count: 28, revenue: 24150 },
    { route: 'Lille → Strasbourg', count: 24, revenue: 21600 },
    { route: 'Nice → Montpellier', count: 19, revenue: 16720 },
  ];

  const performanceMetrics = [
    { label: 'Délai moyen', value: '2.3j', trend: 'down', color: 'green' },
    { label: 'Taux de retard', value: '5.2%', trend: 'down', color: 'green' },
    { label: 'Coût par km', value: '0.89€', trend: 'down', color: 'green' },
    { label: 'Taux de remplissage', value: '87%', trend: 'up', color: 'blue' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                SABLOGISTIQUE
              </Link>
              <span className="ml-4 text-gray-500">Analytics & Reporting</span>
            </div>
            <nav className="flex space-x-8">
              <Link href="/shipper" className="text-gray-600 hover:text-indigo-600">
                Tableau de bord
              </Link>
              <Button variant="outline" size="sm">
                Déconnexion
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics & Reporting</h1>
            <p className="text-gray-600">Analysez vos performances et optimisez vos opérations</p>
          </div>
          <div className="flex space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="7d">7 derniers jours</option>
              <option value="30d">30 derniers jours</option>
              <option value="90d">3 derniers mois</option>
              <option value="1y">12 derniers mois</option>
            </select>
            <Button variant="outline">
              Exporter PDF
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <MetricsCard
            title="Chiffre d'affaires"
            value={`${metrics.totalRevenue.toLocaleString()} €`}
            change={{ value: 12.5, type: 'increase', period: 'le mois dernier' }}
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
            color="green"
          />
          
          <MetricsCard
            title="Expéditions"
            value={metrics.totalShipments}
            change={{ value: 8.2, type: 'increase', period: 'le mois dernier' }}
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            }
            color="blue"
          />
          
          <MetricsCard
            title="Coût moyen"
            value={`${metrics.avgCost} €`}
            change={{ value: 3.1, type: 'decrease', period: 'le mois dernier' }}
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            color="yellow"
          />
          
          <MetricsCard
            title="Satisfaction"
            value={`${metrics.satisfaction}/5`}
            change={{ value: 0.3, type: 'increase', period: 'le mois dernier' }}
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
            }
            color="indigo"
          />
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <SimpleChart
            title="Évolution du chiffre d'affaires"
            data={revenueData}
            type="line"
            height={250}
          />
          
          <SimpleChart
            title="Répartition par type d'offre"
            data={shipmentsByType}
            type="bar"
            height={250}
          />
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-6">Indicateurs de performance</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {performanceMetrics.map((metric, index) => (
              <div key={index} className="text-center">
                <div className={`text-2xl font-bold ${
                  metric.color === 'green' ? 'text-green-600' : 'text-blue-600'
                }`}>
                  {metric.value}
                </div>
                <div className="text-sm text-gray-600">{metric.label}</div>
                <div className={`text-xs ${
                  metric.trend === 'up' ? 'text-green-500' : 'text-red-500'
                } flex items-center justify-center mt-1`}>
                  {metric.trend === 'up' ? '↗' : '↘'} Amélioration
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Routes */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">Top 5 des routes</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Route
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expéditions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Chiffre d&apos;affaires
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Moyenne par expédition
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {topRoutes.map((route, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {route.route}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {route.count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {route.revenue.toLocaleString()} €
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {Math.round(route.revenue / route.count).toLocaleString()} €
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
