const http = require('http');

console.log('🧪 TEST COMPLET DES APIS SABLOGISTIQUE');
console.log('=====================================\n');

// Fonction pour faire une requête HTTP simple
function testAPI(path, description) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    console.log(`📋 ${description}`);
    console.log(`🔗 GET http://localhost:3000${path}`);

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        if (res.statusCode === 200) {
          try {
            const jsonData = JSON.parse(data);
            console.log(`✅ Status: ${res.statusCode} - Données récupérées`);
            
            if (Array.isArray(jsonData)) {
              console.log(`📊 ${jsonData.length} éléments trouvés`);
              
              // Afficher un aperçu des données
              if (jsonData.length > 0) {
                const first = jsonData[0];
                const keys = Object.keys(first).slice(0, 3);
                console.log(`📝 Aperçu: ${keys.join(', ')}...`);
              }
            } else {
              console.log(`📝 Objet retourné avec ${Object.keys(jsonData).length} propriétés`);
            }
          } catch (error) {
            console.log(`✅ Status: ${res.statusCode} - Réponse non-JSON`);
          }
        } else {
          console.log(`❌ Status: ${res.statusCode}`);
        }
        
        console.log(''); // Ligne vide
        resolve(res.statusCode === 200);
      });
    });

    req.on('error', (error) => {
      console.log(`❌ Erreur: ${error.message}`);
      console.log(''); // Ligne vide
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ Timeout`);
      console.log(''); // Ligne vide
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Tests des APIs
async function runAllTests() {
  console.log('🚀 Démarrage des tests...\n');

  const tests = [
    { path: '/api/users', description: 'Test API Users' },
    { path: '/api/freight-offers', description: 'Test API Freight Offers' },
    { path: '/api/bids', description: 'Test API Bids' },
    { path: '/', description: 'Test Page d\'accueil' }
  ];

  let successCount = 0;
  
  for (const test of tests) {
    const success = await testAPI(test.path, test.description);
    if (success) successCount++;
    
    // Petite pause entre les tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Résumé final
  console.log('📊 RÉSUMÉ DES TESTS');
  console.log('==================');
  console.log(`✅ Tests réussis: ${successCount}/${tests.length}`);
  console.log(`❌ Tests échoués: ${tests.length - successCount}/${tests.length}`);
  
  if (successCount === tests.length) {
    console.log('\n🎉 TOUS LES TESTS SONT PASSÉS !');
    console.log('L\'application SABLOGISTIQUE est entièrement fonctionnelle !');
  } else {
    console.log('\n⚠️  Certains tests ont échoué');
    console.log('Vérifiez que le serveur Next.js est démarré: npm run dev');
  }

  console.log('\n🔗 URLs disponibles:');
  console.log('- Interface: http://localhost:3000');
  console.log('- API Users: http://localhost:3000/api/users');
  console.log('- API Offers: http://localhost:3000/api/freight-offers');
  console.log('- API Bids: http://localhost:3000/api/bids');
  
  console.log('\n💡 Commandes utiles:');
  console.log('- npm run dev          # Démarrer le serveur');
  console.log('- npm run db:studio    # Interface Prisma');
  console.log('- npm run db:seed      # Repeupler la base');
}

// Exécuter tous les tests
runAllTests().catch(console.error);
