'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

// Mock data pour la démonstration
const mockAvailableOffers = [
  {
    id: '1',
    title: 'Transport Paris - Lyon',
    type: 'SPOT',
    origin: 'Paris, France',
    destination: 'Lyon, France',
    pickupDate: '2024-01-15',
    weight: 2500,
    cargoType: 'Électronique',
    budget: 850,
    distance: 465,
  },
  {
    id: '2',
    title: 'Livraison Bordeaux - Nantes',
    type: 'TERM',
    origin: 'Bordeaux, France',
    destination: 'Nantes, France',
    pickupDate: '2024-01-18',
    weight: 3200,
    cargoType: 'Alimentaire',
    budget: 720,
    distance: 347,
  },
];

const mockMyBids = [
  {
    id: '1',
    offerId: '1',
    title: 'Transport Paris - Lyon',
    bidAmount: 800,
    status: 'PENDING',
    submittedAt: '2024-01-10',
  },
  {
    id: '2',
    offerId: '3',
    title: 'Livraison Marseille - Nice',
    bidAmount: 450,
    status: 'ACCEPTED',
    submittedAt: '2024-01-08',
  },
];

const mockStats = {
  availableOffers: 24,
  myBids: 8,
  acceptedJobs: 3,
  totalEarnings: 12450,
};

export default function CarrierDashboard() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                SABLOGISTIQUE
              </Link>
              <span className="ml-4 text-gray-500">Tableau de bord Transporteur</span>
            </div>
            <nav className="flex space-x-8">
              <Link href="/carrier/offers" className="text-gray-600 hover:text-indigo-600">
                Offres disponibles
              </Link>
              <Link href="/carrier/tracking" className="text-gray-600 hover:text-indigo-600">
                Mes transports
              </Link>
              <Link href="/carrier/vehicles" className="text-gray-600 hover:text-indigo-600">
                Mes véhicules
              </Link>
              <Link href="/profile" className="text-gray-600 hover:text-indigo-600">
                Profil
              </Link>
              <Button variant="outline" size="sm">
                Déconnexion
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Vue d&apos;ensemble
            </button>
            <button
              onClick={() => setActiveTab('offers')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'offers'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Offres disponibles
            </button>
            <button
              onClick={() => setActiveTab('bids')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'bids'
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Mes offres
            </button>
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Offres disponibles</dt>
                        <dd className="text-lg font-medium text-gray-900">{mockStats.availableOffers}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Mes offres</dt>
                        <dd className="text-lg font-medium text-gray-900">{mockStats.myBids}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Transports acceptés</dt>
                        <dd className="text-lg font-medium text-gray-900">{mockStats.acceptedJobs}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Revenus totaux</dt>
                        <dd className="text-lg font-medium text-gray-900">{mockStats.totalEarnings.toLocaleString()} €</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <Link href="/carrier/offers">
                  <Button className="w-full">
                    Parcourir les offres
                  </Button>
                </Link>
                <Link href="/carrier/vehicles">
                  <Button variant="outline" className="w-full">
                    Gérer mes véhicules
                  </Button>
                </Link>
                <Link href="/carrier/tracking">
                  <Button variant="outline" className="w-full">
                    Suivre mes transports
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Available Offers Tab */}
        {activeTab === 'offers' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Offres de fret disponibles</h3>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">Filtrer</Button>
                <Button variant="outline" size="sm">Trier</Button>
              </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-2">
              {mockAvailableOffers.map((offer) => (
                <div key={offer.id} className="bg-white shadow rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="text-lg font-medium text-gray-900">{offer.title}</h4>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {offer.type}
                    </span>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Trajet:</span> {offer.origin} → {offer.destination}
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Date de collecte:</span> {new Date(offer.pickupDate).toLocaleDateString('fr-FR')}
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Marchandise:</span> {offer.cargoType} • {offer.weight} kg
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Distance:</span> {offer.distance} km
                    </p>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-green-600">{offer.budget} €</span>
                    <Button size="sm">Faire une offre</Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* My Bids Tab */}
        {activeTab === 'bids' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Mes offres soumises</h3>

            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {mockMyBids.map((bid) => (
                  <li key={bid.id}>
                    <div className="px-4 py-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <p className="text-sm font-medium text-indigo-600 truncate">{bid.title}</p>
                          <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            bid.status === 'ACCEPTED' ? 'bg-green-100 text-green-800' : 
                            bid.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 
                            'bg-red-100 text-red-800'
                          }`}>
                            {bid.status === 'ACCEPTED' ? 'Acceptée' : 
                             bid.status === 'PENDING' ? 'En attente' : 'Refusée'}
                          </span>
                        </div>
                        <div className="ml-2 flex-shrink-0">
                          <p className="text-sm font-medium text-gray-900">{bid.bidAmount} €</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">
                          Soumise le {new Date(bid.submittedAt).toLocaleDateString('fr-FR')}
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
