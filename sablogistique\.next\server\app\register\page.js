(()=>{var e={};e.id=454,e.ids=[454],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1159:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>l});var s=t(5239),o=t(8088),i=t(8170),n=t.n(i),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4530)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\register\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3852:()=>{},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>d});var s=t(7413),o=t(2376),i=t.n(o),n=t(8726),a=t.n(n);t(1135);let d={title:"Create Next App",description:"Generated by create next app"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:e})})}},4530:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\register\\page.tsx","default")},4543:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},5098:(e,r,t)=>{Promise.resolve().then(t.bind(t,6167))},5186:(e,r,t)=>{Promise.resolve().then(t.bind(t,4530))},5708:()=>{},6167:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(687),o=t(3210),i=t(5814),n=t.n(i),a=t(7320);function d(){let[e,r]=(0,o.useState)({name:"",email:"",password:"",confirmPassword:"",company:"",phone:"",role:"SHIPPER"}),[t,i]=(0,o.useState)(!1),d=async r=>{r.preventDefault(),i(!0),console.log("Registration attempt:",e),setTimeout(()=>{i(!1)},1e3)},l=e=>{let{name:t,value:s}=e.target;r(e=>({...e,[t]:s}))};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Cr\xe9er votre compte SABLOGISTIQUE"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Ou"," ",(0,s.jsx)(n(),{href:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"connectez-vous \xe0 votre compte existant"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:d,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Type de compte"}),(0,s.jsxs)("select",{id:"role",name:"role",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",value:e.role,onChange:l,children:[(0,s.jsx)("option",{value:"SHIPPER",children:"Affr\xe9teur (Exp\xe9diteur)"}),(0,s.jsx)("option",{value:"CARRIER",children:"Transporteur"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Nom complet"}),(0,s.jsx)("input",{id:"name",name:"name",type:"text",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Votre nom complet",value:e.name,onChange:l})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Adresse email"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>",value:e.email,onChange:l})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700",children:"Entreprise"}),(0,s.jsx)("input",{id:"company",name:"company",type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nom de votre entreprise",value:e.company,onChange:l})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"T\xe9l\xe9phone"}),(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"+33 1 23 45 67 89",value:e.phone,onChange:l})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Mot de passe"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Mot de passe",value:e.password,onChange:l})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirmer le mot de passe"}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Confirmer le mot de passe",value:e.confirmPassword,onChange:l})]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"ml-2 block text-sm text-gray-900",children:["J'accepte les"," ",(0,s.jsx)(n(),{href:"/terms",className:"text-indigo-600 hover:text-indigo-500",children:"conditions d'utilisation"})," ","et la"," ",(0,s.jsx)(n(),{href:"/privacy",className:"text-indigo-600 hover:text-indigo-500",children:"politique de confidentialit\xe9"})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(a.$,{type:"submit",className:"group relative w-full",disabled:t,children:t?"Cr\xe9ation du compte...":"Cr\xe9er mon compte"})})]})]})})}},7320:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(687);t(3210);let o=({variant:e="primary",size:r="md",className:t,children:o,...i})=>(0,s.jsx)("button",{className:function(...e){return e.filter(Boolean).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[e],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[r],t),...i,children:o})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9815:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,145,567],()=>t(1159));module.exports=s})();