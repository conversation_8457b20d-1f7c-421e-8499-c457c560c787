const fs = require('fs');
const path = require('path');

console.log('🧪 TEST COMPLET DE L\'APPLICATION SABLOGISTIQUE');
console.log('================================================\n');

// Test 1: Vérification de la structure des fichiers
console.log('📁 Test 1: Structure des fichiers');
const requiredFiles = [
  'package.json',
  'next.config.ts',
  'tailwind.config.ts',
  'tsconfig.json',
  'src/app/page.tsx',
  'src/app/layout.tsx',
  'src/app/login/page.tsx',
  'src/app/register/page.tsx',
  'src/app/shipper/page.tsx',
  'src/app/carrier/page.tsx',
  'src/app/tracking/page.tsx',
  'src/app/demo/page.tsx',
  'src/app/analytics/page.tsx',
  'src/components/ui/Button.tsx',
  'src/lib/utils.ts',
  'src/types/index.ts',
  'prisma/schema.prisma'
];

let filesOk = 0;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
    filesOk++;
  } else {
    console.log(`❌ ${file} - MANQUANT`);
  }
});

console.log(`\n📊 Résultat: ${filesOk}/${requiredFiles.length} fichiers présents\n`);

// Test 2: Vérification du package.json
console.log('📦 Test 2: Configuration package.json');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const requiredDeps = ['next', 'react', 'react-dom'];
  const requiredDevDeps = ['typescript', '@types/node', '@types/react', 'tailwindcss'];
  
  console.log('Dependencies:');
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - MANQUANT`);
    }
  });
  
  console.log('\nDev Dependencies:');
  requiredDevDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - MANQUANT`);
    }
  });
  
  console.log('\nScripts:');
  const requiredScripts = ['dev', 'build', 'start', 'lint'];
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`✅ ${script}: ${packageJson.scripts[script]}`);
    } else {
      console.log(`❌ ${script} - MANQUANT`);
    }
  });
  
} catch (error) {
  console.log(`❌ Erreur lors de la lecture du package.json: ${error.message}`);
}

console.log('\n');

// Test 3: Vérification des composants React
console.log('⚛️  Test 3: Validation des composants React');

const componentFiles = [
  'src/app/page.tsx',
  'src/app/login/page.tsx',
  'src/app/register/page.tsx',
  'src/app/shipper/page.tsx',
  'src/app/carrier/page.tsx',
  'src/components/ui/Button.tsx'
];

componentFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // Vérifications basiques
    const hasExport = content.includes('export default') || content.includes('export const') || content.includes('export function');
    const hasReact = content.includes('React') || content.includes('useState') || content.includes('useEffect');
    const hasJSX = content.includes('<') && content.includes('>');
    
    if (hasExport && hasJSX) {
      console.log(`✅ ${file} - Composant valide`);
    } else {
      console.log(`⚠️  ${file} - Problème potentiel (export: ${hasExport}, JSX: ${hasJSX})`);
    }
  }
});

console.log('\n');

// Test 4: Vérification des types TypeScript
console.log('🔷 Test 4: Types TypeScript');

if (fs.existsSync('src/types/index.ts')) {
  const typesContent = fs.readFileSync('src/types/index.ts', 'utf8');
  
  const requiredTypes = ['User', 'FreightOffer', 'Vehicle', 'Bid', 'TrackingEvent'];
  requiredTypes.forEach(type => {
    if (typesContent.includes(`interface ${type}`) || typesContent.includes(`type ${type}`)) {
      console.log(`✅ Type ${type} défini`);
    } else {
      console.log(`❌ Type ${type} manquant`);
    }
  });
} else {
  console.log('❌ Fichier types/index.ts manquant');
}

console.log('\n');

// Test 5: Vérification du schéma Prisma
console.log('🗄️  Test 5: Schéma de base de données Prisma');

if (fs.existsSync('prisma/schema.prisma')) {
  const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
  
  const requiredModels = ['User', 'FreightOffer', 'Vehicle', 'Bid', 'TrackingEvent', 'Location'];
  requiredModels.forEach(model => {
    if (schemaContent.includes(`model ${model}`)) {
      console.log(`✅ Modèle ${model} défini`);
    } else {
      console.log(`❌ Modèle ${model} manquant`);
    }
  });
  
  // Vérifier la configuration de base
  if (schemaContent.includes('generator client')) {
    console.log('✅ Générateur Prisma configuré');
  } else {
    console.log('❌ Générateur Prisma manquant');
  }
  
  if (schemaContent.includes('datasource db')) {
    console.log('✅ Source de données configurée');
  } else {
    console.log('❌ Source de données manquante');
  }
} else {
  console.log('❌ Fichier schema.prisma manquant');
}

console.log('\n');

// Test 6: Vérification des pages principales
console.log('📄 Test 6: Pages principales');

const pages = [
  { file: 'src/app/page.tsx', name: 'Page d\'accueil' },
  { file: 'src/app/login/page.tsx', name: 'Page de connexion' },
  { file: 'src/app/register/page.tsx', name: 'Page d\'inscription' },
  { file: 'src/app/shipper/page.tsx', name: 'Tableau de bord affréteur' },
  { file: 'src/app/carrier/page.tsx', name: 'Tableau de bord transporteur' },
  { file: 'src/app/tracking/page.tsx', name: 'Page de suivi' },
  { file: 'src/app/demo/page.tsx', name: 'Page de démonstration' },
  { file: 'src/app/analytics/page.tsx', name: 'Page d\'analytics' }
];

pages.forEach(page => {
  if (fs.existsSync(page.file)) {
    const content = fs.readFileSync(page.file, 'utf8');
    
    // Vérifier que c'est un composant Next.js valide
    const hasDefaultExport = content.includes('export default');
    const hasJSX = content.includes('return (') || content.includes('return <');
    
    if (hasDefaultExport && hasJSX) {
      console.log(`✅ ${page.name} - OK`);
    } else {
      console.log(`⚠️  ${page.name} - Structure incomplète`);
    }
  } else {
    console.log(`❌ ${page.name} - Fichier manquant`);
  }
});

console.log('\n');

// Test 7: Résumé final
console.log('📋 RÉSUMÉ DU TEST');
console.log('==================');

const totalTests = 6;
let passedTests = 0;

// Compter les tests réussis (simplifié)
if (filesOk >= requiredFiles.length * 0.8) passedTests++;
if (fs.existsSync('package.json')) passedTests++;
if (fs.existsSync('src/components/ui/Button.tsx')) passedTests++;
if (fs.existsSync('src/types/index.ts')) passedTests++;
if (fs.existsSync('prisma/schema.prisma')) passedTests++;
if (fs.existsSync('src/app/page.tsx')) passedTests++;

console.log(`✅ Tests réussis: ${passedTests}/${totalTests}`);
console.log(`📊 Score: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
  console.log('\n🎉 TOUS LES TESTS SONT PASSÉS !');
  console.log('✨ L\'application SABLOGISTIQUE est prête !');
} else if (passedTests >= totalTests * 0.8) {
  console.log('\n✅ LA PLUPART DES TESTS SONT PASSÉS');
  console.log('⚠️  Quelques ajustements mineurs peuvent être nécessaires');
} else {
  console.log('\n⚠️  PLUSIEURS TESTS ONT ÉCHOUÉ');
  console.log('🔧 Des corrections sont nécessaires avant le déploiement');
}

console.log('\n💡 Pour démarrer l\'application:');
console.log('   npm run dev');
console.log('   Puis ouvrir: http://localhost:3000');
