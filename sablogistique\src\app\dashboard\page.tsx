import './dashboard.css';

export default function Dashboard() {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f8fafc',
      padding: '2rem'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          marginBottom: '2rem'
        }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            color: '#1e40af',
            margin: 0,
            marginBottom: '0.5rem'
          }}>
            SABLOGISTIQUE
          </h1>
          <p style={{
            fontSize: '1.125rem',
            color: '#64748b',
            margin: 0
          }}>
            Tableau de Bord - Plateforme de Transport de Fret
          </p>
        </div>

        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            borderLeft: '4px solid #10b981'
          }}>
            <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Utilisateurs</h3>
            <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981', margin: 0 }}>4</p>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>2 Affréteurs, 2 Transporteurs</p>
          </div>

          <div style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            borderLeft: '4px solid #3b82f6'
          }}>
            <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Offres de Fret</h3>
            <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6', margin: 0 }}>2</p>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>Publiées par Jean Dupont</p>
          </div>

          <div style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            borderLeft: '4px solid #f59e0b'
          }}>
            <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Enchères</h3>
            <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b', margin: 0 }}>2</p>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>Soumises par les transporteurs</p>
          </div>

          <div style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            borderLeft: '4px solid #8b5cf6'
          }}>
            <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Véhicules</h3>
            <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#8b5cf6', margin: 0 }}>2</p>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>Flotte disponible</p>
          </div>
        </div>

        {/* API Links */}
        <div style={{
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          marginBottom: '2rem'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#374151',
            margin: 0,
            marginBottom: '1rem'
          }}>
            APIs Disponibles
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1rem'
          }}>
            <a
              href="/api/users"
              target="_blank"
              className="api-link"
            >
              <strong>GET /api/users</strong>
              <br />
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                Gestion des utilisateurs (affréteurs et transporteurs)
              </span>
            </a>

            <a
              href="/api/freight-offers"
              target="_blank"
              className="api-link"
            >
              <strong>GET /api/freight-offers</strong>
              <br />
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                Offres de transport de marchandises
              </span>
            </a>

            <a
              href="/api/bids"
              target="_blank"
              className="api-link"
            >
              <strong>GET /api/bids</strong>
              <br />
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                Enchères et offres de prix des transporteurs
              </span>
            </a>
          </div>
        </div>

        {/* User Profiles */}
        <div style={{
          backgroundColor: 'white',
          padding: '2rem',
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#374151',
            margin: 0,
            marginBottom: '1rem'
          }}>
            Utilisateurs Actifs
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
            gap: '1rem'
          }}>
            <div className="user-card shipper">
              <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Jean Dupont</h3>
              <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
                <strong>SHIPPER</strong> - TechCorp SARL<br />
                📦 2 offres publiées
              </p>
            </div>

            <div className="user-card carrier">
              <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Pierre Durand</h3>
              <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
                <strong>CARRIER</strong> - Logistique Rapide<br />
                🚛 1 véhicule, 💰 1 enchère
              </p>
            </div>

            <div className="user-card carrier">
              <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Marie Martin</h3>
              <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
                <strong>CARRIER</strong> - Transport Express<br />
                🚚 1 véhicule, 💰 1 enchère
              </p>
            </div>

            <div className="user-card new">
              <h3 style={{ margin: 0, marginBottom: '0.5rem', color: '#374151' }}>Utilisateur Test</h3>
              <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
                <strong>SHIPPER</strong> - Test Company<br />
                🆕 Nouveau compte
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
