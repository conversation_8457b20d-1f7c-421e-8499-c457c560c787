/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // Désactiver les fonctionnalités expérimentales qui peuvent causer des problèmes
  },
  typescript: {
    // Ignorer les erreurs TypeScript pendant le build (temporaire)
    ignoreBuildErrors: true,
  },
  eslint: {
    // Ignorer les erreurs ESLint pendant le build (temporaire)
    ignoreDuringBuilds: true,
  },
}

module.exports = nextConfig