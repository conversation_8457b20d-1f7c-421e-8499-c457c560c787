# Database
DATABASE_URL="postgresql://username:password@localhost:5432/sablogistique?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

FACEBOOK_CLIENT_ID="your-facebook-client-id"
FACEBOOK_CLIENT_SECRET="your-facebook-client-secret"

# Email (optional)
EMAIL_SERVER_HOST="smtp.example.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-email-password"
EMAIL_FROM="<EMAIL>"

# Maps API (for geolocation)
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"
MAPBOX_ACCESS_TOKEN="your-mapbox-access-token"

# Redis (for caching and real-time features)
REDIS_URL="redis://localhost:6379"

# File uploads
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="10485760" # 10MB

# API Keys for external services
TRACKING_API_KEY="your-tracking-api-key"
WEATHER_API_KEY="your-weather-api-key"

# Environment
NODE_ENV="development"
