(()=>{var e={};e.id=812,e.ids=[812],e.modules={440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2886:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\shipper\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\shipper\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3823:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(5239),i=t(8088),n=t(8170),a=t.n(n),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["shipper",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2886)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\shipper\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\shipper\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/shipper/page",pathname:"/shipper",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3852:()=>{},3873:e=>{"use strict";e.exports=require("path")},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>d});var r=t(7413),i=t(2376),n=t.n(i),a=t(8726),l=t.n(a);t(1135);let d={title:"Create Next App",description:"Generated by create next app"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${l().variable} antialiased`,children:e})})}},4543:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},5268:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(687),i=t(3210),n=t(5814),a=t.n(n),l=t(7320);let d=[{id:"1",title:"Transport Paris - Lyon",type:"SPOT",status:"ACTIVE",origin:"Paris, France",destination:"Lyon, France",pickupDate:"2024-01-15",weight:2500,cargoType:"\xc9lectronique",bidsCount:3,budget:850},{id:"2",title:"Livraison Marseille - Toulouse",type:"TERM",status:"ASSIGNED",origin:"Marseille, France",destination:"Toulouse, France",pickupDate:"2024-01-20",weight:1800,cargoType:"Textile",bidsCount:5,budget:650}],o={totalOffers:12,activeOffers:5,completedOffers:7,totalSavings:15420};function c(){let[e,s]=(0,i.useState)("overview");return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(a(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"SABLOGISTIQUE"}),(0,r.jsx)("span",{className:"ml-4 text-gray-500",children:"Tableau de bord Affr\xe9teur"})]}),(0,r.jsxs)("nav",{className:"flex space-x-8",children:[(0,r.jsx)(a(),{href:"/shipper/offers/new",className:"text-gray-600 hover:text-indigo-600",children:"Nouvelle offre"}),(0,r.jsx)(a(),{href:"/shipper/tracking",className:"text-gray-600 hover:text-indigo-600",children:"Suivi"}),(0,r.jsx)(a(),{href:"/profile",className:"text-gray-600 hover:text-indigo-600",children:"Profil"}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"D\xe9connexion"})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 mb-8",children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsx)("button",{onClick:()=>s("overview"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"overview"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Vue d'ensemble"}),(0,r.jsx)("button",{onClick:()=>s("offers"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"offers"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Mes offres"}),(0,r.jsx)("button",{onClick:()=>s("analytics"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"analytics"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Analyses"})]})}),"overview"===e&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total des offres"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:o.totalOffers})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Offres actives"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:o.activeOffers})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Transports termin\xe9s"}),(0,r.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:o.completedOffers})]})})]})})}),(0,r.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,r.jsx)("div",{className:"p-5",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})})}),(0,r.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,r.jsxs)("dl",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"\xc9conomies r\xe9alis\xe9es"}),(0,r.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[o.totalSavings.toLocaleString()," €"]})]})})]})})})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Actions rapides"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,r.jsx)(a(),{href:"/shipper/offers/new",children:(0,r.jsx)(l.$,{className:"w-full",children:"Publier une nouvelle offre"})}),(0,r.jsx)(a(),{href:"/shipper/search",children:(0,r.jsx)(l.$,{variant:"outline",className:"w-full",children:"Rechercher des transporteurs"})}),(0,r.jsx)(a(),{href:"/shipper/tracking",children:(0,r.jsx)(l.$,{variant:"outline",className:"w-full",children:"Suivre mes exp\xe9ditions"})})]})]})]}),"offers"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Mes offres de fret"}),(0,r.jsx)(a(),{href:"/shipper/offers/new",children:(0,r.jsx)(l.$,{children:"Nouvelle offre"})})]}),(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:d.map(e=>(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"px-4 py-4 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-indigo-600 truncate",children:e.title}),(0,r.jsx)("span",{className:`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"ACTIVE"===e.status?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"}`,children:"ACTIVE"===e.status?"Actif":"Attribu\xe9"})]}),(0,r.jsx)("div",{className:"ml-2 flex-shrink-0 flex",children:(0,r.jsxs)("p",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800",children:[e.bidsCount," offres"]})})]}),(0,r.jsxs)("div",{className:"mt-2 sm:flex sm:justify-between",children:[(0,r.jsxs)("div",{className:"sm:flex",children:[(0,r.jsxs)("p",{className:"flex items-center text-sm text-gray-500",children:[e.origin," → ",e.destination]}),(0,r.jsxs)("p",{className:"mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6",children:[e.weight," kg • ",e.cargoType]})]}),(0,r.jsx)("div",{className:"mt-2 flex items-center text-sm text-gray-500 sm:mt-0",children:(0,r.jsxs)("p",{children:["Budget: ",e.budget," €"]})})]})]})},e.id))})})]}),"analytics"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Analyses et rapports"}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Les analyses d\xe9taill\xe9es seront disponibles prochainement."})})]})]})]})}},5708:()=>{},6022:(e,s,t)=>{Promise.resolve().then(t.bind(t,2886))},7320:(e,s,t)=>{"use strict";t.d(s,{$:()=>i});var r=t(687);t(3210);let i=({variant:e="primary",size:s="md",className:t,children:i,...n})=>(0,r.jsx)("button",{className:function(...e){return e.filter(Boolean).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[e],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[s],t),...n,children:i})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9582:(e,s,t)=>{Promise.resolve().then(t.bind(t,5268))},9815:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,145,567],()=>t(3823));module.exports=r})();