{"name": "sablogistique", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.10.1", "autoprefixer": "^10.4.21", "next": "^15.1.3", "postcss": "^8.5.6", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.1.11", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "15.1.3", "tsx": "^4.20.3", "typescript": "^5"}}