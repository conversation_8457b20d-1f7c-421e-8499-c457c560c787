{"name": "sablogistique", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm run docker:up && npm run db:push && npm run db:seed"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.10.1", "clsx": "^2.1.1", "next": "15.3.4", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "rimraf": "^6.0.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}