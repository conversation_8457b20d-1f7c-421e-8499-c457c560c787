{"name": "sablogistique", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.10.1", "next": "^15.1.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "15.1.3", "typescript": "^5"}}