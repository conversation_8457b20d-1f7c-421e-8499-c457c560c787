(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[245],{2573:(e,s,t)=>{"use strict";t.d(s,{$:()=>r});var i=t(5155);t(2115);let r=e=>{let{variant:s="primary",size:t="md",className:r,children:a,...l}=e;return(0,i.jsx)("button",{className:function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter(Boolean).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[s],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[t],r),...l,children:a})}},6003:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var i=t(5155),r=t(2115),a=t(6874),l=t.n(a),n=t(2573);let d=[{id:"1",title:"Transport Paris - Lyon",type:"SPOT",origin:"Paris, France",destination:"Lyon, France",pickupDate:"2024-01-15",weight:2500,cargoType:"\xc9lectronique",budget:850,distance:465},{id:"2",title:"Livraison Bordeaux - Nantes",type:"TERM",origin:"Bordeaux, France",destination:"Nantes, France",pickupDate:"2024-01-18",weight:3200,cargoType:"Alimentaire",budget:720,distance:347}],c=[{id:"1",offerId:"1",title:"Transport Paris - Lyon",bidAmount:800,status:"PENDING",submittedAt:"2024-01-10"},{id:"2",offerId:"3",title:"Livraison Marseille - Nice",bidAmount:450,status:"ACCEPTED",submittedAt:"2024-01-08"}],o={availableOffers:24,myBids:8,acceptedJobs:3,totalEarnings:12450};function x(){let[e,s]=(0,r.useState)("overview");return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)("header",{className:"bg-white shadow",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(l(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"SABLOGISTIQUE"}),(0,i.jsx)("span",{className:"ml-4 text-gray-500",children:"Tableau de bord Transporteur"})]}),(0,i.jsxs)("nav",{className:"flex space-x-8",children:[(0,i.jsx)(l(),{href:"/carrier/offers",className:"text-gray-600 hover:text-indigo-600",children:"Offres disponibles"}),(0,i.jsx)(l(),{href:"/carrier/tracking",className:"text-gray-600 hover:text-indigo-600",children:"Mes transports"}),(0,i.jsx)(l(),{href:"/carrier/vehicles",className:"text-gray-600 hover:text-indigo-600",children:"Mes v\xe9hicules"}),(0,i.jsx)(l(),{href:"/profile",className:"text-gray-600 hover:text-indigo-600",children:"Profil"}),(0,i.jsx)(n.$,{variant:"outline",size:"sm",children:"D\xe9connexion"})]})]})})}),(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsx)("div",{className:"border-b border-gray-200 mb-8",children:(0,i.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,i.jsx)("button",{onClick:()=>s("overview"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("overview"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Vue d'ensemble"}),(0,i.jsx)("button",{onClick:()=>s("offers"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("offers"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Offres disponibles"}),(0,i.jsx)("button",{onClick:()=>s("bids"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("bids"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Mes offres"})]})}),"overview"===e&&(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,i.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,i.jsx)("div",{className:"p-5",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center",children:(0,i.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,i.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,i.jsxs)("dl",{children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Offres disponibles"}),(0,i.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:o.availableOffers})]})})]})})}),(0,i.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,i.jsx)("div",{className:"p-5",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,i.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})})}),(0,i.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,i.jsxs)("dl",{children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Mes offres"}),(0,i.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:o.myBids})]})})]})})}),(0,i.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,i.jsx)("div",{className:"p-5",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,i.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})}),(0,i.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,i.jsxs)("dl",{children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Transports accept\xe9s"}),(0,i.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:o.acceptedJobs})]})})]})})}),(0,i.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,i.jsx)("div",{className:"p-5",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,i.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})})}),(0,i.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,i.jsxs)("dl",{children:[(0,i.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Revenus totaux"}),(0,i.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[o.totalEarnings.toLocaleString()," €"]})]})})]})})})]}),(0,i.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Actions rapides"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,i.jsx)(l(),{href:"/carrier/offers",children:(0,i.jsx)(n.$,{className:"w-full",children:"Parcourir les offres"})}),(0,i.jsx)(l(),{href:"/carrier/vehicles",children:(0,i.jsx)(n.$,{variant:"outline",className:"w-full",children:"G\xe9rer mes v\xe9hicules"})}),(0,i.jsx)(l(),{href:"/carrier/tracking",children:(0,i.jsx)(n.$,{variant:"outline",className:"w-full",children:"Suivre mes transports"})})]})]})]}),"offers"===e&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Offres de fret disponibles"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(n.$,{variant:"outline",size:"sm",children:"Filtrer"}),(0,i.jsx)(n.$,{variant:"outline",size:"sm",children:"Trier"})]})]}),(0,i.jsx)("div",{className:"grid gap-6 lg:grid-cols-2",children:d.map(e=>(0,i.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,i.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.title}),(0,i.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.type})]}),(0,i.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"font-medium",children:"Trajet:"})," ",e.origin," → ",e.destination]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"font-medium",children:"Date de collecte:"})," ",new Date(e.pickupDate).toLocaleDateString("fr-FR")]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"font-medium",children:"Marchandise:"})," ",e.cargoType," • ",e.weight," kg"]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"font-medium",children:"Distance:"})," ",e.distance," km"]})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[e.budget," €"]}),(0,i.jsx)(n.$,{size:"sm",children:"Faire une offre"})]})]},e.id))})]}),"bids"===e&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Mes offres soumises"}),(0,i.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,i.jsx)("ul",{className:"divide-y divide-gray-200",children:c.map(e=>(0,i.jsx)("li",{children:(0,i.jsxs)("div",{className:"px-4 py-4 sm:px-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-indigo-600 truncate",children:e.title}),(0,i.jsx)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("ACCEPTED"===e.status?"bg-green-100 text-green-800":"PENDING"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"ACCEPTED"===e.status?"Accept\xe9e":"PENDING"===e.status?"En attente":"Refus\xe9e"})]}),(0,i.jsx)("div",{className:"ml-2 flex-shrink-0",children:(0,i.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[e.bidAmount," €"]})})]}),(0,i.jsx)("div",{className:"mt-2",children:(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["Soumise le ",new Date(e.submittedAt).toLocaleDateString("fr-FR")]})})]})},e.id))})})]})]})]})}},6195:(e,s,t)=>{Promise.resolve().then(t.bind(t,6003))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,441,684,358],()=>s(6195)),_N_E=e.O()}]);