/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bids/route";
exports.ids = ["app/api/bids/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbids%2Froute&page=%2Fapi%2Fbids%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbids%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbids%2Froute&page=%2Fapi%2Fbids%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbids%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_xampp1_htdocs_SABLOGISTIQUE_sablogistique_src_app_api_bids_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/bids/route.ts */ \"(rsc)/./src/app/api/bids/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bids/route\",\n        pathname: \"/api/bids\",\n        filename: \"route\",\n        bundlePath: \"app/api/bids/route\"\n    },\n    resolvedPagePath: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\api\\\\bids\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_xampp1_htdocs_SABLOGISTIQUE_sablogistique_src_app_api_bids_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbids%2Froute&page=%2Fapi%2Fbids%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbids%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/bids/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/bids/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Schéma de validation pour la création d'enchère\nconst createBidSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().positive(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().default('EUR'),\n    proposedPickupDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime(),\n    proposedDeliveryDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime(),\n    message: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    freightOfferId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n    carrierId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string()\n});\n// Schéma pour la mise à jour du statut\nconst updateBidStatusSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    status: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        'PENDING',\n        'ACCEPTED',\n        'REJECTED',\n        'WITHDRAWN'\n    ])\n});\n// GET /api/bids - Récupérer les enchères\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const freightOfferId = searchParams.get('freightOfferId');\n        const carrierId = searchParams.get('carrierId');\n        const status = searchParams.get('status');\n        const bids = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bid.findMany({\n            where: {\n                ...freightOfferId && {\n                    freightOfferId\n                },\n                ...carrierId && {\n                    carrierId\n                },\n                ...status && {\n                    status: status\n                }\n            },\n            include: {\n                freightOffer: {\n                    include: {\n                        origin: true,\n                        destination: true,\n                        shipper: {\n                            select: {\n                                id: true,\n                                name: true,\n                                company: true\n                            }\n                        }\n                    }\n                },\n                carrier: {\n                    select: {\n                        id: true,\n                        name: true,\n                        company: true,\n                        phone: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(bids);\n    } catch (error) {\n        console.error('Erreur lors de la récupération des enchères:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Erreur interne du serveur'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/bids - Créer une nouvelle enchère\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const validatedData = createBidSchema.parse(body);\n        // Vérifier que l'offre existe et est active\n        const freightOffer = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.freightOffer.findUnique({\n            where: {\n                id: validatedData.freightOfferId\n            }\n        });\n        if (!freightOffer) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Offre de fret non trouvée'\n            }, {\n                status: 404\n            });\n        }\n        if (freightOffer.status !== 'ACTIVE') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Cette offre n\\'est plus active'\n            }, {\n                status: 400\n            });\n        }\n        // Vérifier que le transporteur n'a pas déjà fait une offre\n        const existingBid = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bid.findFirst({\n            where: {\n                freightOfferId: validatedData.freightOfferId,\n                carrierId: validatedData.carrierId,\n                status: {\n                    not: 'WITHDRAWN'\n                }\n            }\n        });\n        if (existingBid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Vous avez déjà soumis une offre pour ce transport'\n            }, {\n                status: 400\n            });\n        }\n        const bid = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bid.create({\n            data: {\n                amount: validatedData.amount,\n                currency: validatedData.currency,\n                proposedPickupDate: new Date(validatedData.proposedPickupDate),\n                proposedDeliveryDate: new Date(validatedData.proposedDeliveryDate),\n                message: validatedData.message,\n                freightOfferId: validatedData.freightOfferId,\n                carrierId: validatedData.carrierId\n            },\n            include: {\n                freightOffer: {\n                    include: {\n                        origin: true,\n                        destination: true\n                    }\n                },\n                carrier: {\n                    select: {\n                        id: true,\n                        name: true,\n                        company: true\n                    }\n                }\n            }\n        });\n        // Créer une notification pour l'affréteur\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.notification.create({\n            data: {\n                type: 'BID_RECEIVED',\n                title: 'Nouvelle offre reçue',\n                message: `Vous avez reçu une nouvelle offre de ${bid.carrier.company || bid.carrier.name} pour ${bid.freightOffer.title}`,\n                userId: freightOffer.shipperId\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(bid, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Données invalides',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error('Erreur lors de la création de l\\'enchère:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Erreur interne du serveur'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/bids/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'query',\n        'error',\n        'warn'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFFN0MsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBSztRQUFDO1FBQVM7UUFBUztLQUFPO0FBQ2pDLEdBQUU7QUFFRixJQUFJQyxJQUFxQyxFQUFFSixnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFx4YW1wcDFcXGh0ZG9jc1xcU0FCTE9HSVNUSVFVRVxcc2FibG9naXN0aXF1ZVxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCdcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWRcbn1cblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCh7XG4gIGxvZzogWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10sXG59KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbids%2Froute&page=%2Fapi%2Fbids%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbids%2Froute.ts&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();