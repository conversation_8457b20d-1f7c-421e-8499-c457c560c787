const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 CONFIGURATION DE LA BASE DE DONNÉES SABLOGISTIQUE');
console.log('====================================================\n');

// Vérifier si Docker est installé
function checkDocker() {
  try {
    execSync('docker --version', { stdio: 'ignore' });
    console.log('✅ Docker est installé');
    return true;
  } catch (error) {
    console.log('❌ Docker n\'est pas installé ou non accessible');
    console.log('💡 Veuillez installer Docker Desktop: https://www.docker.com/products/docker-desktop');
    return false;
  }
}

// Vérifier si le fichier .env.local existe
function checkEnvFile() {
  if (fs.existsSync('.env.local')) {
    console.log('✅ Fichier .env.local trouvé');
    return true;
  } else {
    console.log('⚠️  Fichier .env.local non trouvé');
    console.log('💡 Copie du fichier .env.example vers .env.local...');
    try {
      fs.copyFileSync('.env.example', '.env.local');
      console.log('✅ Fichier .env.local créé');
      return true;
    } catch (error) {
      console.log('❌ Erreur lors de la création du fichier .env.local');
      return false;
    }
  }
}

// Démarrer PostgreSQL avec Docker
function startPostgreSQL() {
  try {
    console.log('🐳 Démarrage de PostgreSQL avec Docker...');
    execSync('docker-compose up -d postgres', { stdio: 'inherit' });
    console.log('✅ PostgreSQL démarré avec succès');
    
    // Attendre que PostgreSQL soit prêt
    console.log('⏳ Attente que PostgreSQL soit prêt...');
    let retries = 30;
    while (retries > 0) {
      try {
        execSync('docker-compose exec -T postgres pg_isready -U postgres', { stdio: 'ignore' });
        console.log('✅ PostgreSQL est prêt');
        break;
      } catch (error) {
        retries--;
        if (retries === 0) {
          throw new Error('PostgreSQL n\'est pas prêt après 30 tentatives');
        }
        // Attendre 1 seconde
        execSync('timeout 1 2>nul || sleep 1', { stdio: 'ignore' });
      }
    }
    return true;
  } catch (error) {
    console.log('❌ Erreur lors du démarrage de PostgreSQL:', error.message);
    return false;
  }
}

// Générer le client Prisma
function generatePrismaClient() {
  try {
    console.log('🔧 Génération du client Prisma...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Client Prisma généré');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de la génération du client Prisma');
    return false;
  }
}

// Appliquer le schéma à la base de données
function pushSchema() {
  try {
    console.log('📊 Application du schéma à la base de données...');
    execSync('npx prisma db push', { stdio: 'inherit' });
    console.log('✅ Schéma appliqué avec succès');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de l\'application du schéma');
    return false;
  }
}

// Peupler la base de données avec des données de test
function seedDatabase() {
  try {
    console.log('🌱 Peuplement de la base de données...');
    execSync('npx prisma db seed', { stdio: 'inherit' });
    console.log('✅ Base de données peuplée avec succès');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors du peuplement de la base de données');
    console.log('💡 Vous pouvez essayer manuellement: npm run db:seed');
    return false;
  }
}

// Fonction principale
async function main() {
  console.log('🔍 Vérification des prérequis...\n');
  
  // Vérifications
  const dockerOk = checkDocker();
  const envOk = checkEnvFile();
  
  if (!dockerOk || !envOk) {
    console.log('\n❌ Prérequis non satisfaits. Veuillez corriger les problèmes ci-dessus.');
    process.exit(1);
  }
  
  console.log('\n🚀 Démarrage de la configuration...\n');
  
  // Configuration étape par étape
  const steps = [
    { name: 'Démarrage PostgreSQL', fn: startPostgreSQL },
    { name: 'Génération client Prisma', fn: generatePrismaClient },
    { name: 'Application du schéma', fn: pushSchema },
    { name: 'Peuplement de la base', fn: seedDatabase },
  ];
  
  for (const step of steps) {
    console.log(`\n📋 ${step.name}...`);
    const success = step.fn();
    if (!success) {
      console.log(`\n❌ Échec lors de: ${step.name}`);
      console.log('🛠️  Veuillez corriger le problème et relancer le script');
      process.exit(1);
    }
  }
  
  console.log('\n🎉 CONFIGURATION TERMINÉE AVEC SUCCÈS !');
  console.log('=====================================\n');
  
  console.log('📋 PROCHAINES ÉTAPES:');
  console.log('1. Démarrer l\'application: npm run dev');
  console.log('2. Ouvrir: http://localhost:3000');
  console.log('3. Tester les APIs: http://localhost:3000/api/users');
  console.log('4. Interface Prisma Studio: npm run db:studio');
  console.log('5. Interface pgAdmin: http://localhost:8080 (<EMAIL> / admin123)');
  
  console.log('\n🔗 URLS UTILES:');
  console.log('- Application: http://localhost:3000');
  console.log('- API Users: http://localhost:3000/api/users');
  console.log('- API Offers: http://localhost:3000/api/freight-offers');
  console.log('- API Bids: http://localhost:3000/api/bids');
  console.log('- Prisma Studio: http://localhost:5555 (après npm run db:studio)');
  console.log('- pgAdmin: http://localhost:8080');
  
  console.log('\n💡 COMMANDES UTILES:');
  console.log('- npm run db:studio    # Interface graphique Prisma');
  console.log('- npm run db:seed      # Repeupler la base');
  console.log('- npm run db:reset     # Réinitialiser la base');
  console.log('- docker-compose logs postgres  # Logs PostgreSQL');
}

// Exécuter le script
main().catch(console.error);
