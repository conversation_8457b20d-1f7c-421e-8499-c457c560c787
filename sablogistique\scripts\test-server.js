const http = require('http');

function testServer(port = 3000) {
  return new Promise((resolve, reject) => {
    const req = http.get(`http://localhost:${port}`, (res) => {
      console.log(`✅ Serveur répond sur le port ${port}`);
      console.log(`📊 Status: ${res.statusCode}`);
      console.log(`📋 Headers:`, res.headers);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ Erreur de connexion sur le port ${port}:`, err.message);
      reject(false);
    });

    req.setTimeout(5000, () => {
      console.log(`⏰ Timeout - Le serveur ne répond pas sur le port ${port}`);
      req.destroy();
      reject(false);
    });
  });
}

async function main() {
  console.log('🔍 Test de connectivité du serveur...');
  
  try {
    await testServer(3000);
    console.log('🎉 Le serveur fonctionne correctement !');
  } catch (error) {
    console.log('💡 Solutions possibles:');
    console.log('   1. Vérifiez que le serveur est démarré: npm run dev');
    console.log('   2. Vérifiez les logs du serveur pour des erreurs');
    console.log('   3. Essayez un autre port: npm run dev -- -p 3001');
    console.log('   4. Nettoyez les caches: npm run clean');
  }
}

main();
