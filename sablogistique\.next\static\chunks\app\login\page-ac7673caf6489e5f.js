(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{2573:(e,s,r)=>{"use strict";r.d(s,{$:()=>a});var t=r(5155);r(2115);let a=e=>{let{variant:s="primary",size:r="md",className:a,children:l,...i}=e;return(0,t.jsx)("button",{className:function(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return s.filter(Boolean).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[s],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[r],a),...i,children:l})}},7834:(e,s,r)=>{Promise.resolve().then(r.bind(r,9690))},9690:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(5155),a=r(2115),l=r(6874),i=r.n(l),n=r(2573);function o(){let[e,s]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[o,c]=(0,a.useState)(!1),d=async s=>{s.preventDefault(),c(!0),console.log("Login attempt:",{email:e,password:r}),setTimeout(()=>{c(!1)},1e3)};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Connexion \xe0 SABLOGISTIQUE"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Ou"," ",(0,t.jsx)(i(),{href:"/register",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"cr\xe9ez votre compte"})]})]}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:d,children:[(0,t.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Adresse email"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Adresse email",value:e,onChange:e=>s(e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Mot de passe"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Mot de passe",value:r,onChange:e=>l(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Se souvenir de moi"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(i(),{href:"/forgot-password",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"Mot de passe oubli\xe9 ?"})})]}),(0,t.jsx)("div",{children:(0,t.jsx)(n.$,{type:"submit",className:"group relative w-full",disabled:o,children:o?"Connexion...":"Se connecter"})}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-gray-50 text-gray-500",children:"Ou continuez avec"})})]}),(0,t.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",className:"w-full",children:[(0,t.jsxs)("svg",{className:"h-5 w-5 mr-2",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,t.jsxs)(n.$,{variant:"outline",className:"w-full",children:[(0,t.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Facebook"]})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[874,441,684,358],()=>s(7834)),_N_E=e.O()}]);