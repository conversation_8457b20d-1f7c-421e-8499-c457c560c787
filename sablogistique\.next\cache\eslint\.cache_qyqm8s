[{"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\carrier\\page.tsx": "1", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\layout.tsx": "2", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\login\\page.tsx": "3", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\page.tsx": "4", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\register\\page.tsx": "5", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\shipper\\page.tsx": "6", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\tracking\\page.tsx": "7", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\components\\ui\\Button.tsx": "8", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\lib\\utils.ts": "9", "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\types\\index.ts": "10"}, {"size": 14052, "mtime": 1751187504414, "results": "11", "hashOfConfig": "12"}, {"size": 689, "mtime": 1750824769382, "results": "13", "hashOfConfig": "12"}, {"size": 5813, "mtime": 1751187385299, "results": "14", "hashOfConfig": "12"}, {"size": 8696, "mtime": 1751187320276, "results": "15", "hashOfConfig": "12"}, {"size": 7406, "mtime": 1751187411996, "results": "16", "hashOfConfig": "12"}, {"size": 12274, "mtime": 1751187455611, "results": "17", "hashOfConfig": "12"}, {"size": 12406, "mtime": 1751187551933, "results": "18", "hashOfConfig": "12"}, {"size": 1230, "mtime": 1751187335743, "results": "19", "hashOfConfig": "12"}, {"size": 804, "mtime": 1751187717940, "results": "20", "hashOfConfig": "12"}, {"size": 2676, "mtime": 1751187359719, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "9ketdj", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\carrier\\page.tsx", ["52"], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\layout.tsx", [], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\login\\page.tsx", [], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\page.tsx", ["53", "54"], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\register\\page.tsx", ["55", "56"], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\shipper\\page.tsx", ["57"], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\tracking\\page.tsx", ["58"], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\components\\ui\\Button.tsx", [], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\lib\\utils.ts", [], [], "C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\types\\index.ts", ["59"], [], {"ruleId": "60", "severity": 2, "message": "61", "line": 109, "column": 20, "nodeType": "62", "messageId": "63", "suggestions": "64"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 74, "column": 78, "nodeType": "62", "messageId": "63", "suggestions": "65"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 101, "column": 105, "nodeType": "62", "messageId": "63", "suggestions": "66"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 176, "column": 16, "nodeType": "62", "messageId": "63", "suggestions": "67"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 178, "column": 29, "nodeType": "62", "messageId": "63", "suggestions": "68"}, {"ruleId": "60", "severity": 2, "message": "61", "line": 89, "column": 20, "nodeType": "62", "messageId": "63", "suggestions": "69"}, {"ruleId": "70", "severity": 2, "message": "71", "line": 3, "column": 20, "nodeType": null, "messageId": "72", "endLine": 3, "endColumn": 29}, {"ruleId": "73", "severity": 2, "message": "74", "line": 77, "column": 29, "nodeType": "75", "messageId": "76", "endLine": 77, "endColumn": 32, "suggestions": "77"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["78", "79", "80", "81"], ["82", "83", "84", "85"], ["86", "87", "88", "89"], ["90", "91", "92", "93"], ["94", "95", "96", "97"], ["98", "99", "100", "101"], "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["102", "103"], {"messageId": "104", "data": "105", "fix": "106", "desc": "107"}, {"messageId": "104", "data": "108", "fix": "109", "desc": "110"}, {"messageId": "104", "data": "111", "fix": "112", "desc": "113"}, {"messageId": "104", "data": "114", "fix": "115", "desc": "116"}, {"messageId": "104", "data": "117", "fix": "118", "desc": "107"}, {"messageId": "104", "data": "119", "fix": "120", "desc": "110"}, {"messageId": "104", "data": "121", "fix": "122", "desc": "113"}, {"messageId": "104", "data": "123", "fix": "124", "desc": "116"}, {"messageId": "104", "data": "125", "fix": "126", "desc": "107"}, {"messageId": "104", "data": "127", "fix": "128", "desc": "110"}, {"messageId": "104", "data": "129", "fix": "130", "desc": "113"}, {"messageId": "104", "data": "131", "fix": "132", "desc": "116"}, {"messageId": "104", "data": "133", "fix": "134", "desc": "107"}, {"messageId": "104", "data": "135", "fix": "136", "desc": "110"}, {"messageId": "104", "data": "137", "fix": "138", "desc": "113"}, {"messageId": "104", "data": "139", "fix": "140", "desc": "116"}, {"messageId": "104", "data": "141", "fix": "142", "desc": "107"}, {"messageId": "104", "data": "143", "fix": "144", "desc": "110"}, {"messageId": "104", "data": "145", "fix": "146", "desc": "113"}, {"messageId": "104", "data": "147", "fix": "148", "desc": "116"}, {"messageId": "104", "data": "149", "fix": "150", "desc": "107"}, {"messageId": "104", "data": "151", "fix": "152", "desc": "110"}, {"messageId": "104", "data": "153", "fix": "154", "desc": "113"}, {"messageId": "104", "data": "155", "fix": "156", "desc": "116"}, {"messageId": "157", "fix": "158", "desc": "159"}, {"messageId": "160", "fix": "161", "desc": "162"}, "replaceWithAlt", {"alt": "163"}, {"range": "164", "text": "165"}, "Replace with `&apos;`.", {"alt": "166"}, {"range": "167", "text": "168"}, "Replace with `&lsquo;`.", {"alt": "169"}, {"range": "170", "text": "171"}, "Replace with `&#39;`.", {"alt": "172"}, {"range": "173", "text": "174"}, "Replace with `&rsquo;`.", {"alt": "163"}, {"range": "175", "text": "176"}, {"alt": "166"}, {"range": "177", "text": "178"}, {"alt": "169"}, {"range": "179", "text": "180"}, {"alt": "172"}, {"range": "181", "text": "182"}, {"alt": "163"}, {"range": "183", "text": "184"}, {"alt": "166"}, {"range": "185", "text": "186"}, {"alt": "169"}, {"range": "187", "text": "188"}, {"alt": "172"}, {"range": "189", "text": "190"}, {"alt": "163"}, {"range": "191", "text": "192"}, {"alt": "166"}, {"range": "193", "text": "194"}, {"alt": "169"}, {"range": "195", "text": "196"}, {"alt": "172"}, {"range": "197", "text": "198"}, {"alt": "163"}, {"range": "199", "text": "200"}, {"alt": "166"}, {"range": "201", "text": "202"}, {"alt": "169"}, {"range": "203", "text": "204"}, {"alt": "172"}, {"range": "205", "text": "206"}, {"alt": "163"}, {"range": "207", "text": "165"}, {"alt": "166"}, {"range": "208", "text": "168"}, {"alt": "169"}, {"range": "209", "text": "171"}, {"alt": "172"}, {"range": "210", "text": "174"}, "suggestUnknown", {"range": "211", "text": "212"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "213", "text": "214"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "&apos;", [3143, 3185], "\n              Vue d&apos;ensemble\n            ", "&lsquo;", [3143, 3185], "\n              Vue d&lsquo;ensemble\n            ", "&#39;", [3143, 3185], "\n              Vue d&#39;ensemble\n            ", "&rsquo;", [3143, 3185], "\n              Vue d&rsquo;ensemble\n            ", [3545, 3674], "\n                Spot, à terme et international. Gestion complète des appels d&apos;offres nationaux et internationaux.\n              ", [3545, 3674], "\n                Spot, à terme et international. Gestion complète des appels d&lsquo;offres nationaux et internationaux.\n              ", [3545, 3674], "\n                Spot, à terme et international. Gestion complète des appels d&#39;offres nationaux et internationaux.\n              ", [3545, 3674], "\n                Spot, à terme et international. Gestion complète des appels d&rsquo;offres nationaux et internationaux.\n              ", [5420, 5552], "\n                Réduction des trajets à vide et optimisation des capacités de transport pour maximiser l&apos;efficacité.\n              ", [5420, 5552], "\n                Réduction des trajets à vide et optimisation des capacités de transport pour maximiser l&lsquo;efficacité.\n              ", [5420, 5552], "\n                Réduction des trajets à vide et optimisation des capacités de transport pour maximiser l&#39;efficacité.\n              ", [5420, 5552], "\n                Réduction des trajets à vide et optimisation des capacités de transport pour maximiser l&rsquo;efficacité.\n              ", [6671, 6699], "\n              J&apos;accepte les", [6671, 6699], "\n              J&lsquo;accepte les", [6671, 6699], "\n              J&#39;accepte les", [6671, 6699], "\n              J&rsquo;accepte les", [6789, 6845], "\n                conditions d&apos;utilisation\n              ", [6789, 6845], "\n                conditions d&lsquo;utilisation\n              ", [6789, 6845], "\n                conditions d&#39;utilisation\n              ", [6789, 6845], "\n                conditions d&rsquo;utilisation\n              ", [2694, 2736], [2694, 2736], [2694, 2736], [2694, 2736], [1630, 1633], "unknown", [1630, 1633], "never"]