version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: sablogistique-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: sablogistique
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - sablogistique-network

  # Optionnel: Interface d'administration de base de données
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: sablogistique-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - sablogistique-network

volumes:
  postgres_data:

networks:
  sablogistique-network:
    driver: bridge
