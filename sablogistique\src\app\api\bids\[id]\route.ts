import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateBidStatusSchema = z.object({
  status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED', 'WITHDRAWN']),
})

// GET /api/bids/[id] - Récupérer une enchère spécifique
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const bid = await prisma.bid.findUnique({
      where: { id: params.id },
      include: {
        freightOffer: {
          include: {
            origin: true,
            destination: true,
            shipper: {
              select: {
                id: true,
                name: true,
                company: true,
              }
            }
          }
        },
        carrier: {
          select: {
            id: true,
            name: true,
            company: true,
            phone: true,
          }
        }
      }
    })

    if (!bid) {
      return NextResponse.json(
        { error: 'Enchère non trouvée' },
        { status: 404 }
      )
    }

    return NextResponse.json(bid)
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'enchère:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

// PATCH /api/bids/[id] - Mettre à jour le statut d'une enchère
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { status } = updateBidStatusSchema.parse(body)

    const existingBid = await prisma.bid.findUnique({
      where: { id: params.id },
      include: {
        freightOffer: {
          include: {
            shipper: true
          }
        },
        carrier: true
      }
    })

    if (!existingBid) {
      return NextResponse.json(
        { error: 'Enchère non trouvée' },
        { status: 404 }
      )
    }

    // Si l'enchère est acceptée, rejeter toutes les autres enchères pour cette offre
    if (status === 'ACCEPTED') {
      await prisma.bid.updateMany({
        where: {
          freightOfferId: existingBid.freightOfferId,
          id: { not: params.id },
          status: 'PENDING'
        },
        data: { status: 'REJECTED' }
      })

      // Mettre à jour l'offre de fret pour l'assigner au transporteur
      await prisma.freightOffer.update({
        where: { id: existingBid.freightOfferId },
        data: {
          status: 'ASSIGNED',
          carrierId: existingBid.carrierId
        }
      })

      // Créer un événement de suivi initial
      await prisma.trackingEvent.create({
        data: {
          type: 'PICKUP',
          description: 'Transport assigné - En attente de collecte',
          freightOfferId: existingBid.freightOfferId,
        }
      })
    }

    const updatedBid = await prisma.bid.update({
      where: { id: params.id },
      data: { status },
      include: {
        freightOffer: {
          include: {
            origin: true,
            destination: true,
          }
        },
        carrier: {
          select: {
            id: true,
            name: true,
            company: true,
          }
        }
      }
    })

    // Créer des notifications appropriées
    if (status === 'ACCEPTED') {
      // Notification pour le transporteur accepté
      await prisma.notification.create({
        data: {
          type: 'BID_ACCEPTED',
          title: 'Offre acceptée !',
          message: `Votre offre pour ${existingBid.freightOffer.title} a été acceptée`,
          userId: existingBid.carrierId,
        }
      })

      // Notifications pour les transporteurs rejetés
      const rejectedBids = await prisma.bid.findMany({
        where: {
          freightOfferId: existingBid.freightOfferId,
          id: { not: params.id },
          status: 'REJECTED'
        }
      })

      for (const rejectedBid of rejectedBids) {
        await prisma.notification.create({
          data: {
            type: 'BID_REJECTED',
            title: 'Offre non retenue',
            message: `Votre offre pour ${existingBid.freightOffer.title} n'a pas été retenue`,
            userId: rejectedBid.carrierId,
          }
        })
      }
    }

    return NextResponse.json(updatedBid)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Erreur lors de la mise à jour de l\'enchère:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

// DELETE /api/bids/[id] - Supprimer une enchère (retirer)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const bid = await prisma.bid.findUnique({
      where: { id: params.id }
    })

    if (!bid) {
      return NextResponse.json(
        { error: 'Enchère non trouvée' },
        { status: 404 }
      )
    }

    if (bid.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Seules les enchères en attente peuvent être retirées' },
        { status: 400 }
      )
    }

    await prisma.bid.update({
      where: { id: params.id },
      data: { status: 'WITHDRAWN' }
    })

    return NextResponse.json({ message: 'Enchère retirée avec succès' })
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'enchère:', error)
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
