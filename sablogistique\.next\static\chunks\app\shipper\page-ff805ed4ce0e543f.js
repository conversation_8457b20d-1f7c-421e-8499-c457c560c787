(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[812],{2573:(e,s,r)=>{"use strict";r.d(s,{$:()=>i});var t=r(5155);r(2115);let i=e=>{let{variant:s="primary",size:r="md",className:i,children:l,...a}=e;return(0,t.jsx)("button",{className:function(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return s.filter(<PERSON><PERSON>an).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[s],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[r],i),...a,children:l})}},2881:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(5155),i=r(2115),l=r(6874),a=r.n(l),n=r(2573);let d=[{id:"1",title:"Transport Paris - Lyon",type:"SPOT",status:"ACTIVE",origin:"Paris, France",destination:"Lyon, France",pickupDate:"2024-01-15",weight:2500,cargoType:"\xc9lectronique",bidsCount:3,budget:850},{id:"2",title:"Livraison Marseille - Toulouse",type:"TERM",status:"ASSIGNED",origin:"Marseille, France",destination:"Toulouse, France",pickupDate:"2024-01-20",weight:1800,cargoType:"Textile",bidsCount:5,budget:650}],c={totalOffers:12,activeOffers:5,completedOffers:7,totalSavings:15420};function o(){let[e,s]=(0,i.useState)("overview");return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(a(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"SABLOGISTIQUE"}),(0,t.jsx)("span",{className:"ml-4 text-gray-500",children:"Tableau de bord Affr\xe9teur"})]}),(0,t.jsxs)("nav",{className:"flex space-x-8",children:[(0,t.jsx)(a(),{href:"/shipper/offers/new",className:"text-gray-600 hover:text-indigo-600",children:"Nouvelle offre"}),(0,t.jsx)(a(),{href:"/shipper/tracking",className:"text-gray-600 hover:text-indigo-600",children:"Suivi"}),(0,t.jsx)(a(),{href:"/profile",className:"text-gray-600 hover:text-indigo-600",children:"Profil"}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:"D\xe9connexion"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)("div",{className:"border-b border-gray-200 mb-8",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsx)("button",{onClick:()=>s("overview"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("overview"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Vue d'ensemble"}),(0,t.jsx)("button",{onClick:()=>s("offers"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("offers"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Mes offres"}),(0,t.jsx)("button",{onClick:()=>s("analytics"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("analytics"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Analyses"})]})}),"overview"===e&&(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total des offres"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.totalOffers})]})})]})})}),(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Offres actives"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.activeOffers})]})})]})})}),(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Transports termin\xe9s"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.completedOffers})]})})]})})}),(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"\xc9conomies r\xe9alis\xe9es"}),(0,t.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[c.totalSavings.toLocaleString()," €"]})]})})]})})})]}),(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Actions rapides"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,t.jsx)(a(),{href:"/shipper/offers/new",children:(0,t.jsx)(n.$,{className:"w-full",children:"Publier une nouvelle offre"})}),(0,t.jsx)(a(),{href:"/shipper/search",children:(0,t.jsx)(n.$,{variant:"outline",className:"w-full",children:"Rechercher des transporteurs"})}),(0,t.jsx)(a(),{href:"/shipper/tracking",children:(0,t.jsx)(n.$,{variant:"outline",className:"w-full",children:"Suivre mes exp\xe9ditions"})})]})]})]}),"offers"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Mes offres de fret"}),(0,t.jsx)(a(),{href:"/shipper/offers/new",children:(0,t.jsx)(n.$,{children:"Nouvelle offre"})})]}),(0,t.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,t.jsx)("ul",{className:"divide-y divide-gray-200",children:d.map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)("div",{className:"px-4 py-4 sm:px-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-indigo-600 truncate",children:e.title}),(0,t.jsx)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("ACTIVE"===e.status?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:"ACTIVE"===e.status?"Actif":"Attribu\xe9"})]}),(0,t.jsx)("div",{className:"ml-2 flex-shrink-0 flex",children:(0,t.jsxs)("p",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800",children:[e.bidsCount," offres"]})})]}),(0,t.jsxs)("div",{className:"mt-2 sm:flex sm:justify-between",children:[(0,t.jsxs)("div",{className:"sm:flex",children:[(0,t.jsxs)("p",{className:"flex items-center text-sm text-gray-500",children:[e.origin," → ",e.destination]}),(0,t.jsxs)("p",{className:"mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6",children:[e.weight," kg • ",e.cargoType]})]}),(0,t.jsx)("div",{className:"mt-2 flex items-center text-sm text-gray-500 sm:mt-0",children:(0,t.jsxs)("p",{children:["Budget: ",e.budget," €"]})})]})]})},e.id))})})]}),"analytics"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Analyses et rapports"}),(0,t.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,t.jsx)("p",{className:"text-gray-500",children:"Les analyses d\xe9taill\xe9es seront disponibles prochainement."})})]})]})]})}},8826:(e,s,r)=>{Promise.resolve().then(r.bind(r,2881))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,441,684,358],()=>s(8826)),_N_E=e.O()}]);