'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

export default function DemoPage() {
  const [activeDemo, setActiveDemo] = useState('shipper');

  const demoFeatures = {
    shipper: [
      {
        title: 'Publication d\'offres de fret',
        description: 'Créez et publiez vos offres de transport en quelques clics',
        icon: '📝',
        demo: 'Formulaire intuitif avec géolocalisation automatique'
      },
      {
        title: 'Gestion des enchères',
        description: 'Re<PERSON>vez et comparez les offres des transporteurs',
        icon: '💰',
        demo: 'Système de notation et sélection automatique'
      },
      {
        title: 'Suivi en temps réel',
        description: 'Suivez vos expéditions avec géolocalisation GPS',
        icon: '📍',
        demo: 'Carte interactive avec notifications push'
      },
      {
        title: 'Analytics avancés',
        description: 'Analysez vos coûts et optimisez vos transports',
        icon: '📊',
        demo: 'Tableaux de bord personnalisables'
      }
    ],
    carrier: [
      {
        title: 'Recherche d\'offres',
        description: 'Trouvez des chargements adaptés à votre flotte',
        icon: '🔍',
        demo: 'Filtres avancés par zone, type, capacité'
      },
      {
        title: 'Optimisation des trajets',
        description: 'Réduisez les kilomètres à vide avec l\'IA',
        icon: '🛣️',
        demo: 'Algorithmes d\'optimisation intelligents'
      },
      {
        title: 'Gestion de flotte',
        description: 'Administrez vos véhicules et conducteurs',
        icon: '🚛',
        demo: 'Interface de gestion complète'
      },
      {
        title: 'Revenus optimisés',
        description: 'Maximisez vos profits avec des données précises',
        icon: '💹',
        demo: 'Prédictions de revenus basées sur l\'historique'
      }
    ]
  };

  const stats = {
    users: '2,500+',
    transports: '15,000+',
    savings: '25%',
    satisfaction: '98%'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                SABLOGISTIQUE
              </Link>
              <span className="ml-4 text-gray-500">Démonstration interactive</span>
            </div>
            <nav className="flex space-x-8">
              <Link href="/" className="text-gray-600 hover:text-indigo-600">
                Accueil
              </Link>
              <Link href="/register" className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Commencer
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Découvrez SABLOGISTIQUE en action
          </h1>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Explorez les fonctionnalités de notre plateforme de transport de fret 
            et découvrez comment nous révolutionnons la logistique.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 gap-8 md:grid-cols-4 mb-16">
          <div className="text-center">
            <div className="text-3xl font-bold text-indigo-600">{stats.users}</div>
            <div className="text-sm text-gray-600">Utilisateurs actifs</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-indigo-600">{stats.transports}</div>
            <div className="text-sm text-gray-600">Transports réalisés</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-indigo-600">{stats.savings}</div>
            <div className="text-sm text-gray-600">Économies moyennes</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-indigo-600">{stats.satisfaction}</div>
            <div className="text-sm text-gray-600">Satisfaction client</div>
          </div>
        </div>

        {/* Demo Selector */}
        <div className="flex justify-center mb-12">
          <div className="bg-white rounded-lg p-1 shadow-md">
            <button
              onClick={() => setActiveDemo('shipper')}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                activeDemo === 'shipper'
                  ? 'bg-indigo-600 text-white'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              👤 Affréteur (Expéditeur)
            </button>
            <button
              onClick={() => setActiveDemo('carrier')}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                activeDemo === 'carrier'
                  ? 'bg-indigo-600 text-white'
                  : 'text-gray-600 hover:text-indigo-600'
              }`}
            >
              🚛 Transporteur
            </button>
          </div>
        </div>

        {/* Features Demo */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {demoFeatures[activeDemo as keyof typeof demoFeatures].map((feature, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start space-x-4">
                <div className="text-4xl">{feature.icon}</div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 mb-3">
                    {feature.description}
                  </p>
                  <div className="bg-indigo-50 rounded-md p-3">
                    <p className="text-sm text-indigo-700">
                      <span className="font-medium">Démo :</span> {feature.demo}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Interactive Demo Section */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Simulation en temps réel
          </h2>
          
          {activeDemo === 'shipper' ? (
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  📋 Créer une nouvelle offre de fret
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Origine
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Paris, France"
                      disabled
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Destination
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="Lyon, France"
                      disabled
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Poids (kg)
                    </label>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="2500"
                      disabled
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Budget (€)
                    </label>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="850"
                      disabled
                    />
                  </div>
                </div>
                <div className="mt-4 flex justify-center">
                  <Link href="/shipper/offers/new">
                    <Button>Essayer la création d&apos;offre</Button>
                  </Link>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  💰 Offres reçues (simulation)
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-white rounded border">
                    <div>
                      <span className="font-medium">Transport Express SARL</span>
                      <p className="text-sm text-gray-600">Livraison en 8h • Note: 4.8/5</p>
                    </div>
                    <span className="text-lg font-bold text-green-600">780 €</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-white rounded border">
                    <div>
                      <span className="font-medium">Logistique Rapide</span>
                      <p className="text-sm text-gray-600">Livraison en 12h • Note: 4.6/5</p>
                    </div>
                    <span className="text-lg font-bold text-green-600">720 €</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  🔍 Rechercher des offres de fret
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Zone géographique
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md" disabled>
                      <option>Île-de-France</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type de marchandise
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md" disabled>
                      <option>Tous types</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Capacité min (kg)
                    </label>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="1000"
                      disabled
                    />
                  </div>
                </div>
                <div className="mt-4 flex justify-center">
                  <Link href="/carrier/offers">
                    <Button>Explorer les offres disponibles</Button>
                  </Link>
                </div>
              </div>

              <div className="bg-blue-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  📦 Offres correspondantes (simulation)
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-white rounded border">
                    <div>
                      <span className="font-medium">Transport Paris → Lyon</span>
                      <p className="text-sm text-gray-600">2500 kg • Électronique • 465 km</p>
                    </div>
                    <span className="text-lg font-bold text-blue-600">850 € max</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-white rounded border">
                    <div>
                      <span className="font-medium">Livraison Bordeaux → Nantes</span>
                      <p className="text-sm text-gray-600">3200 kg • Alimentaire • 347 km</p>
                    </div>
                    <span className="text-lg font-bold text-blue-600">720 € max</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* CTA Section */}
        <div className="bg-indigo-600 rounded-lg p-8 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">
            Prêt à révolutionner votre logistique ?
          </h2>
          <p className="text-xl mb-6 opacity-90">
            Rejoignez les milliers d&apos;entreprises qui font confiance à SABLOGISTIQUE
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button variant="outline" className="bg-white text-indigo-600 hover:bg-gray-100">
                Créer un compte gratuit
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" className="border-white text-white hover:bg-indigo-700">
                Demander une démo personnalisée
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
