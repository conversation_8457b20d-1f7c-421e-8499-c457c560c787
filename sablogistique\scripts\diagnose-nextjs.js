const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC NEXT.JS - SABLOGISTIQUE');
console.log('====================================\n');

// Vérifier les fichiers essentiels
const essentialFiles = [
  'src/app/layout.tsx',
  'src/app/page.tsx',
  'src/app/globals.css',
  'next.config.ts',
  'tsconfig.json',
  'tailwind.config.ts',
  'package.json'
];

console.log('📁 Vérification des fichiers essentiels:');
essentialFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const stats = fs.statSync(file);
    console.log(`✅ ${file} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${file} - MANQUANT`);
  }
});

console.log('\n📦 Vérification du package.json:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`✅ Next.js version: ${packageJson.dependencies?.next || 'Non trouvé'}`);
  console.log(`✅ React version: ${packageJson.dependencies?.react || 'Non trouvé'}`);
  console.log(`✅ TypeScript: ${packageJson.devDependencies?.typescript || 'Non trouvé'}`);
} catch (error) {
  console.log(`❌ Erreur lecture package.json: ${error.message}`);
}

console.log('\n🔧 Vérification de la configuration TypeScript:');
try {
  const tsconfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
  console.log(`✅ Compiler options configurées`);
  console.log(`✅ Include: ${JSON.stringify(tsconfig.include)}`);
} catch (error) {
  console.log(`❌ Erreur lecture tsconfig.json: ${error.message}`);
}

console.log('\n🎨 Vérification de Tailwind CSS:');
try {
  if (fs.existsSync('tailwind.config.ts')) {
    console.log('✅ Configuration Tailwind trouvée');
  }
  if (fs.existsSync('src/app/globals.css')) {
    const css = fs.readFileSync('src/app/globals.css', 'utf8');
    if (css.includes('@tailwind')) {
      console.log('✅ Directives Tailwind présentes dans globals.css');
    } else {
      console.log('⚠️  Directives Tailwind manquantes dans globals.css');
    }
  }
} catch (error) {
  console.log(`❌ Erreur vérification Tailwind: ${error.message}`);
}

console.log('\n📄 Vérification du layout principal:');
try {
  const layout = fs.readFileSync('src/app/layout.tsx', 'utf8');
  if (layout.includes('export default')) {
    console.log('✅ Export default présent dans layout.tsx');
  }
  if (layout.includes('RootLayout')) {
    console.log('✅ Fonction RootLayout définie');
  }
  if (layout.includes('children')) {
    console.log('✅ Props children utilisées');
  }
  if (layout.includes('globals.css')) {
    console.log('✅ Import globals.css présent');
  }
} catch (error) {
  console.log(`❌ Erreur lecture layout.tsx: ${error.message}`);
}

console.log('\n📄 Vérification de la page principale:');
try {
  const page = fs.readFileSync('src/app/page.tsx', 'utf8');
  if (page.includes('export default')) {
    console.log('✅ Export default présent dans page.tsx');
  }
  if (page.includes('return')) {
    console.log('✅ Return statement présent');
  }
  if (page.includes('className')) {
    console.log('✅ Classes CSS utilisées');
  }
} catch (error) {
  console.log(`❌ Erreur lecture page.tsx: ${error.message}`);
}

console.log('\n🗂️  Vérification de la structure des dossiers:');
const requiredDirs = [
  'src',
  'src/app',
  'src/components',
  'src/lib',
  'src/types',
  'prisma'
];

requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir}/`);
  } else {
    console.log(`❌ ${dir}/ - MANQUANT`);
  }
});

console.log('\n🔍 Vérification des caches:');
const cacheFiles = [
  '.next',
  'node_modules/.cache',
  '.turbo'
];

cacheFiles.forEach(cache => {
  if (fs.existsSync(cache)) {
    console.log(`⚠️  ${cache} existe (peut causer des problèmes)`);
  } else {
    console.log(`✅ ${cache} absent (bon)`);
  }
});

console.log('\n💡 RECOMMANDATIONS:');
console.log('1. Si des caches existent, les supprimer avec: node scripts/clean.js');
console.log('2. Vérifier que tous les fichiers essentiels sont présents');
console.log('3. Essayer de démarrer en mode développement: npm run dev');
console.log('4. Si problème persiste, réinstaller: rm -rf node_modules && npm install');

console.log('\n🚀 COMMANDES DE RÉCUPÉRATION:');
console.log('node scripts/clean.js        # Nettoyer les caches');
console.log('npm install                  # Réinstaller les dépendances');
console.log('npm run dev                  # Démarrer en mode dev');
console.log('npm run build                # Construire pour production');
