const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 CONFIGURATION RAPIDE AVEC SQLITE');
console.log('===================================\n');

// Modifier le schéma Prisma pour utiliser SQLite
function updateSchemaForSQLite() {
  try {
    console.log('🔧 Configuration du schéma pour SQLite...');
    
    const schemaPath = 'prisma/schema.prisma';
    let schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Remplacer PostgreSQL par SQLite
    schema = schema.replace(
      'provider = "postgresql"',
      'provider = "sqlite"'
    );
    
    schema = schema.replace(
      'url      = env("DATABASE_URL")',
      'url      = "file:./dev.db"'
    );
    
    fs.writeFileSync(schemaPath, schema);
    console.log('✅ Schéma configuré pour SQLite');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de la configuration du schéma:', error.message);
    return false;
  }
}

// Mettre à jour le fichier .env.local
function updateEnvForSQLite() {
  try {
    console.log('🔧 Mise à jour du fichier .env.local...');
    
    const envContent = `# Database Configuration (SQLite pour développement)
DATABASE_URL="file:./dev.db"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="sablogistique-secret-key-2024-very-secure"

# Development Environment
NODE_ENV="development"

# Note: Pour la production, utilisez PostgreSQL
# DATABASE_URL="postgresql://postgres:password@localhost:5432/sablogistique?schema=public"
`;
    
    fs.writeFileSync('.env.local', envContent);
    console.log('✅ Fichier .env.local mis à jour');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de la mise à jour du .env.local:', error.message);
    return false;
  }
}

// Générer le client Prisma
function generatePrismaClient() {
  try {
    console.log('🔧 Génération du client Prisma...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Client Prisma généré');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de la génération du client Prisma');
    return false;
  }
}

// Créer et migrer la base de données
function createAndMigrateDatabase() {
  try {
    console.log('📊 Création et migration de la base de données...');
    execSync('npx prisma db push', { stdio: 'inherit' });
    console.log('✅ Base de données créée et migrée');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors de la création de la base de données');
    return false;
  }
}

// Peupler la base de données
function seedDatabase() {
  try {
    console.log('🌱 Peuplement de la base de données...');
    execSync('npx prisma db seed', { stdio: 'inherit' });
    console.log('✅ Base de données peuplée avec succès');
    return true;
  } catch (error) {
    console.log('❌ Erreur lors du peuplement de la base de données');
    console.log('💡 Vous pouvez essayer manuellement: npm run db:seed');
    return false;
  }
}

// Fonction principale
async function main() {
  console.log('🔍 Configuration rapide avec SQLite pour le développement...\n');
  
  const steps = [
    { name: 'Configuration schéma SQLite', fn: updateSchemaForSQLite },
    { name: 'Mise à jour environnement', fn: updateEnvForSQLite },
    { name: 'Génération client Prisma', fn: generatePrismaClient },
    { name: 'Création base de données', fn: createAndMigrateDatabase },
    { name: 'Peuplement données test', fn: seedDatabase },
  ];
  
  for (const step of steps) {
    console.log(`\n📋 ${step.name}...`);
    const success = step.fn();
    if (!success) {
      console.log(`\n❌ Échec lors de: ${step.name}`);
      console.log('🛠️  Veuillez corriger le problème et relancer le script');
      process.exit(1);
    }
  }
  
  console.log('\n🎉 CONFIGURATION SQLITE TERMINÉE !');
  console.log('=================================\n');
  
  console.log('📋 PROCHAINES ÉTAPES:');
  console.log('1. Démarrer l\'application: npm run dev');
  console.log('2. Ouvrir: http://localhost:3000');
  console.log('3. Tester les APIs: http://localhost:3000/api/users');
  console.log('4. Interface Prisma Studio: npm run db:studio');
  
  console.log('\n🔗 URLS DE TEST:');
  console.log('- Application: http://localhost:3000');
  console.log('- API Users: http://localhost:3000/api/users');
  console.log('- API Offers: http://localhost:3000/api/freight-offers');
  console.log('- API Bids: http://localhost:3000/api/bids');
  
  console.log('\n💡 COMMANDES UTILES:');
  console.log('- npm run db:studio    # Interface graphique Prisma');
  console.log('- npm run db:seed      # Repeupler la base');
  console.log('- npm run db:reset     # Réinitialiser la base');
  
  console.log('\n📝 NOTE:');
  console.log('SQLite est parfait pour le développement.');
  console.log('Pour la production, utilisez PostgreSQL avec Docker ou un service cloud.');
}

// Exécuter le script
main().catch(console.error);
