globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/carrier/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"284":{"*":{"id":"5060","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"2881":{"*":{"id":"5268","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"6003":{"*":{"id":"3557","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6616":{"*":{"id":"6167","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"5814","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}},"9690":{"*":{"id":"9488","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":2093,"name":"*","chunks":["177","static/chunks/app/layout-dfec86b36f971a1d.js"],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7735,"name":"*","chunks":["177","static/chunks/app/layout-dfec86b36f971a1d.js"],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-dfec86b36f971a1d.js"],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\carrier\\page.tsx":{"id":6003,"name":"*","chunks":["874","static/chunks/874-6c932be8bed200c7.js","245","static/chunks/app/carrier/page-1c9efd6e8ff6f9c7.js"],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\login\\page.tsx":{"id":9690,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":6874,"name":"*","chunks":["874","static/chunks/874-6c932be8bed200c7.js","974","static/chunks/app/page-f9783a2bc7485bec.js"],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":6874,"name":"*","chunks":["874","static/chunks/874-6c932be8bed200c7.js","974","static/chunks/app/page-f9783a2bc7485bec.js"],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\register\\page.tsx":{"id":6616,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\shipper\\page.tsx":{"id":2881,"name":"*","chunks":[],"async":false},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\tracking\\page.tsx":{"id":284,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\":[],"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\layout":[{"inlined":false,"path":"static/css/4ef0030ccb2991e6.css"}],"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\page":[],"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\carrier\\page":[]},"rscModuleMapping":{"284":{"*":{"id":"478","name":"*","chunks":[],"async":false}},"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"2881":{"*":{"id":"2886","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"6003":{"*":{"id":"4063","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6616":{"*":{"id":"4530","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"4536","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}},"9690":{"*":{"id":"4934","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}