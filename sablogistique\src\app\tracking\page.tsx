'use client';

import { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/Button';

// Mock data pour la démonstration
const mockShipments = [
  {
    id: 'SH001',
    title: 'Transport Paris - Lyon',
    status: 'IN_TRANSIT',
    origin: 'Paris, France',
    destination: 'Lyon, France',
    currentLocation: 'Mâcon, France',
    progress: 75,
    estimatedArrival: '2024-01-15T14:30:00',
    carrier: 'Transport Express SARL',
    vehicle: 'Camion 20T - AB-123-CD',
    events: [
      {
        id: '1',
        type: 'PICKUP',
        description: 'Marchandise collectée',
        location: 'Paris, France',
        timestamp: '2024-01-14T08:00:00',
      },
      {
        id: '2',
        type: 'IN_TRANSIT',
        description: 'Transport en cours',
        location: 'Mâcon, France',
        timestamp: '2024-01-15T10:30:00',
      },
    ],
  },
  {
    id: 'SH002',
    title: 'Livraison Marseille - Nice',
    status: 'DELIVERED',
    origin: 'Marseille, France',
    destination: 'Nice, France',
    currentLocation: 'Nice, France',
    progress: 100,
    estimatedArrival: '2024-01-12T16:00:00',
    carrier: 'Logistique Méditerranée',
    vehicle: 'Fourgon 3.5T - EF-456-GH',
    events: [
      {
        id: '1',
        type: 'PICKUP',
        description: 'Marchandise collectée',
        location: 'Marseille, France',
        timestamp: '2024-01-12T09:00:00',
      },
      {
        id: '2',
        type: 'DELIVERY',
        description: 'Livraison effectuée',
        location: 'Nice, France',
        timestamp: '2024-01-12T15:45:00',
      },
    ],
  },
];

export default function TrackingPage() {
  const [selectedShipment, setSelectedShipment] = useState(mockShipments[0]);
  const [trackingCode, setTrackingCode] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PICKUP':
        return 'bg-blue-100 text-blue-800';
      case 'IN_TRANSIT':
        return 'bg-yellow-100 text-yellow-800';
      case 'DELIVERED':
        return 'bg-green-100 text-green-800';
      case 'DELAY':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PICKUP':
        return 'Collecte';
      case 'IN_TRANSIT':
        return 'En transit';
      case 'DELIVERED':
        return 'Livré';
      case 'DELAY':
        return 'Retard';
      default:
        return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                SABLOGISTIQUE
              </Link>
              <span className="ml-4 text-gray-500">Suivi des expéditions</span>
            </div>
            <nav className="flex space-x-8">
              <Link href="/shipper" className="text-gray-600 hover:text-indigo-600">
                Tableau de bord
              </Link>
              <Link href="/profile" className="text-gray-600 hover:text-indigo-600">
                Profil
              </Link>
              <Button variant="outline" size="sm">
                Déconnexion
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Section */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Rechercher une expédition</h2>
          <div className="flex gap-4">
            <input
              type="text"
              placeholder="Entrez le code de suivi..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              value={trackingCode}
              onChange={(e) => setTrackingCode(e.target.value)}
            />
            <Button>Rechercher</Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Shipments List */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Mes expéditions</h3>
                <div className="space-y-3">
                  {mockShipments.map((shipment) => (
                    <div
                      key={shipment.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedShipment.id === shipment.id
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedShipment(shipment)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <p className="text-sm font-medium text-gray-900">{shipment.id}</p>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(shipment.status)}`}>
                          {getStatusText(shipment.status)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{shipment.title}</p>
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-indigo-600 h-2 rounded-full"
                            style={{ width: `${shipment.progress}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{shipment.progress}% terminé</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Shipment Details */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{selectedShipment.title}</h3>
                    <p className="text-sm text-gray-500">Code de suivi: {selectedShipment.id}</p>
                  </div>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedShipment.status)}`}>
                    {getStatusText(selectedShipment.status)}
                  </span>
                </div>

                {/* Progress Bar */}
                <div className="mb-6">
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span>{selectedShipment.origin}</span>
                    <span>{selectedShipment.destination}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-indigo-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${selectedShipment.progress}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Départ</span>
                    <span>{selectedShipment.progress}%</span>
                    <span>Arrivée</span>
                  </div>
                </div>

                {/* Current Status */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Position actuelle</h4>
                    <p className="text-sm text-gray-600">{selectedShipment.currentLocation}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Arrivée estimée</h4>
                    <p className="text-sm text-gray-600">
                      {new Date(selectedShipment.estimatedArrival).toLocaleString('fr-FR')}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Transporteur</h4>
                    <p className="text-sm text-gray-600">{selectedShipment.carrier}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Véhicule</h4>
                    <p className="text-sm text-gray-600">{selectedShipment.vehicle}</p>
                  </div>
                </div>

                {/* Map Placeholder */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Carte de suivi</h4>
                  <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
                    <div className="text-center">
                      <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <p className="mt-2 text-sm text-gray-500">Carte interactive disponible prochainement</p>
                    </div>
                  </div>
                </div>

                {/* Timeline */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-4">Historique des événements</h4>
                  <div className="flow-root">
                    <ul className="-mb-8">
                      {selectedShipment.events.map((event, eventIdx) => (
                        <li key={event.id}>
                          <div className="relative pb-8">
                            {eventIdx !== selectedShipment.events.length - 1 ? (
                              <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                            ) : null}
                            <div className="relative flex space-x-3">
                              <div>
                                <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getStatusColor(event.type)}`}>
                                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </span>
                              </div>
                              <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                <div>
                                  <p className="text-sm text-gray-900">{event.description}</p>
                                  <p className="text-sm text-gray-500">{event.location}</p>
                                </div>
                                <div className="text-right text-sm whitespace-nowrap text-gray-500">
                                  {new Date(event.timestamp).toLocaleString('fr-FR')}
                                </div>
                              </div>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
