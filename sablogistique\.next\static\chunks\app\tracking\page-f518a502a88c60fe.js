(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[242],{284:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var i=t(5155),r=t(2115),a=t(6874),n=t.n(a),l=t(2573);let c=[{id:"SH001",title:"Transport Paris - Lyon",status:"IN_TRANSIT",origin:"Paris, France",destination:"Lyon, France",currentLocation:"M\xe2con, France",progress:75,estimatedArrival:"2024-01-15T14:30:00",carrier:"Transport Express SARL",vehicle:"Camion 20T - AB-123-CD",events:[{id:"1",type:"PICKUP",description:"Marchandise collect\xe9e",location:"Paris, France",timestamp:"2024-01-14T08:00:00"},{id:"2",type:"IN_TRANSIT",description:"Transport en cours",location:"M\xe2con, France",timestamp:"2024-01-15T10:30:00"}]},{id:"SH002",title:"Livraison Marseille - Nice",status:"DELIVERED",origin:"Marseille, France",destination:"Nice, France",currentLocation:"Nice, France",progress:100,estimatedArrival:"2024-01-12T16:00:00",carrier:"Logistique M\xe9diterran\xe9e",vehicle:"Fourgon 3.5T - EF-456-GH",events:[{id:"1",type:"PICKUP",description:"Marchandise collect\xe9e",location:"Marseille, France",timestamp:"2024-01-12T09:00:00"},{id:"2",type:"DELIVERY",description:"Livraison effectu\xe9e",location:"Nice, France",timestamp:"2024-01-12T15:45:00"}]}];function d(){let[e,s]=(0,r.useState)(c[0]),[t,a]=(0,r.useState)(""),d=e=>{switch(e){case"PICKUP":return"bg-blue-100 text-blue-800";case"IN_TRANSIT":return"bg-yellow-100 text-yellow-800";case"DELIVERED":return"bg-green-100 text-green-800";case"DELAY":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case"PICKUP":return"Collecte";case"IN_TRANSIT":return"En transit";case"DELIVERED":return"Livr\xe9";case"DELAY":return"Retard";default:return e}};return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,i.jsx)("header",{className:"bg-white shadow",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(n(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"SABLOGISTIQUE"}),(0,i.jsx)("span",{className:"ml-4 text-gray-500",children:"Suivi des exp\xe9ditions"})]}),(0,i.jsxs)("nav",{className:"flex space-x-8",children:[(0,i.jsx)(n(),{href:"/shipper",className:"text-gray-600 hover:text-indigo-600",children:"Tableau de bord"}),(0,i.jsx)(n(),{href:"/profile",className:"text-gray-600 hover:text-indigo-600",children:"Profil"}),(0,i.jsx)(l.$,{variant:"outline",size:"sm",children:"D\xe9connexion"})]})]})})}),(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"bg-white shadow rounded-lg p-6 mb-8",children:[(0,i.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Rechercher une exp\xe9dition"}),(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)("input",{type:"text",placeholder:"Entrez le code de suivi...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",value:t,onChange:e=>a(e.target.value)}),(0,i.jsx)(l.$,{children:"Rechercher"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,i.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Mes exp\xe9ditions"}),(0,i.jsx)("div",{className:"space-y-3",children:c.map(t=>(0,i.jsxs)("div",{className:"p-3 rounded-lg border cursor-pointer transition-colors ".concat(e.id===t.id?"border-indigo-500 bg-indigo-50":"border-gray-200 hover:border-gray-300"),onClick:()=>s(t),children:[(0,i.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-900",children:t.id}),(0,i.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(d(t.status)),children:o(t.status)})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:t.title}),(0,i.jsxs)("div",{className:"mt-2",children:[(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-indigo-600 h-2 rounded-full",style:{width:"".concat(t.progress,"%")}})}),(0,i.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[t.progress,"% termin\xe9"]})]})]},t.id))})]})})}),(0,i.jsx)("div",{className:"lg:col-span-2",children:(0,i.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,i.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-start mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.title}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["Code de suivi: ",e.id]})]}),(0,i.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat(d(e.status)),children:o(e.status)})]}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,i.jsx)("span",{children:e.origin}),(0,i.jsx)("span",{children:e.destination})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,i.jsx)("div",{className:"bg-indigo-600 h-3 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,i.jsx)("span",{children:"D\xe9part"}),(0,i.jsxs)("span",{children:[e.progress,"%"]}),(0,i.jsx)("span",{children:"Arriv\xe9e"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Position actuelle"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.currentLocation})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Arriv\xe9e estim\xe9e"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:new Date(e.estimatedArrival).toLocaleString("fr-FR")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Transporteur"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.carrier})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"V\xe9hicule"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.vehicle})]})]}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Carte de suivi"}),(0,i.jsx)("div",{className:"bg-gray-100 rounded-lg h-64 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsxs)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,i.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Carte interactive disponible prochainement"})]})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-4",children:"Historique des \xe9v\xe9nements"}),(0,i.jsx)("div",{className:"flow-root",children:(0,i.jsx)("ul",{className:"-mb-8",children:e.events.map((s,t)=>(0,i.jsx)("li",{children:(0,i.jsxs)("div",{className:"relative pb-8",children:[t!==e.events.length-1?(0,i.jsx)("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,i.jsxs)("div",{className:"relative flex space-x-3",children:[(0,i.jsx)("div",{children:(0,i.jsx)("span",{className:"h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ".concat(d(s.type)),children:(0,i.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})}),(0,i.jsxs)("div",{className:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-gray-900",children:s.description}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:s.location})]}),(0,i.jsx)("div",{className:"text-right text-sm whitespace-nowrap text-gray-500",children:new Date(s.timestamp).toLocaleString("fr-FR")})]})]})]})},s.id))})})]})]})})})]})]})]})}},2573:(e,s,t)=>{"use strict";t.d(s,{$:()=>r});var i=t(5155);t(2115);let r=e=>{let{variant:s="primary",size:t="md",className:r,children:a,...n}=e;return(0,i.jsx)("button",{className:function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter(Boolean).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[s],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[t],r),...n,children:a})}},5874:(e,s,t)=>{Promise.resolve().then(t.bind(t,284))}},e=>{var s=s=>e(e.s=s);e.O(0,[874,441,684,358],()=>s(5874)),_N_E=e.O()}]);