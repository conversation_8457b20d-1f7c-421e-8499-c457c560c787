/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/demo/page";
exports.ids = ["app/demo/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'demo',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/demo/page\",\n        pathname: \"/demo\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(rsc)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN4YW1wcDElNUMlNUNodGRvY3MlNUMlNUNTQUJMT0dJU1RJUVVFJTVDJTVDc2FibG9naXN0aXF1ZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2RlbW8lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQStHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFx4YW1wcDFcXFxcaHRkb2NzXFxcXFNBQkxPR0lTVElRVUVcXFxcc2FibG9naXN0aXF1ZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFx4YW1wcDFcXGh0ZG9jc1xcU0FCTE9HSVNUSVFVRVxcc2FibG9naXN0aXF1ZVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\demo\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxceGFtcHAxXFxodGRvY3NcXFNBQkxPR0lTVElRVUVcXHNhYmxvZ2lzdGlxdWVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFx4YW1wcDFcXGh0ZG9jc1xcU0FCTE9HSVNUSVFVRVxcc2FibG9naXN0aXF1ZVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/demo/page.tsx */ \"(ssr)/./src/app/demo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN4YW1wcDElNUMlNUNodGRvY3MlNUMlNUNTQUJMT0dJU1RJUVVFJTVDJTVDc2FibG9naXN0aXF1ZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2RlbW8lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQStHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFx4YW1wcDFcXFxcaHRkb2NzXFxcXFNBQkxPR0lTVElRVUVcXFxcc2FibG9naXN0aXF1ZVxcXFxzcmNcXFxcYXBwXFxcXGRlbW9cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp1%5C%5Chtdocs%5C%5CSABLOGISTIQUE%5C%5Csablogistique%5C%5Csrc%5C%5Capp%5C%5Cdemo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DemoPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DemoPage() {\n    const [activeDemo, setActiveDemo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('shipper');\n    const demoFeatures = {\n        shipper: [\n            {\n                title: 'Publication d\\'offres de fret',\n                description: 'Créez et publiez vos offres de transport en quelques clics',\n                icon: '📝',\n                demo: 'Formulaire intuitif avec géolocalisation automatique'\n            },\n            {\n                title: 'Gestion des enchères',\n                description: 'Recevez et comparez les offres des transporteurs',\n                icon: '💰',\n                demo: 'Système de notation et sélection automatique'\n            },\n            {\n                title: 'Suivi en temps réel',\n                description: 'Suivez vos expéditions avec géolocalisation GPS',\n                icon: '📍',\n                demo: 'Carte interactive avec notifications push'\n            },\n            {\n                title: 'Analytics avancés',\n                description: 'Analysez vos coûts et optimisez vos transports',\n                icon: '📊',\n                demo: 'Tableaux de bord personnalisables'\n            }\n        ],\n        carrier: [\n            {\n                title: 'Recherche d\\'offres',\n                description: 'Trouvez des chargements adaptés à votre flotte',\n                icon: '🔍',\n                demo: 'Filtres avancés par zone, type, capacité'\n            },\n            {\n                title: 'Optimisation des trajets',\n                description: 'Réduisez les kilomètres à vide avec l\\'IA',\n                icon: '🛣️',\n                demo: 'Algorithmes d\\'optimisation intelligents'\n            },\n            {\n                title: 'Gestion de flotte',\n                description: 'Administrez vos véhicules et conducteurs',\n                icon: '🚛',\n                demo: 'Interface de gestion complète'\n            },\n            {\n                title: 'Revenus optimisés',\n                description: 'Maximisez vos profits avec des données précises',\n                icon: '💹',\n                demo: 'Prédictions de revenus basées sur l\\'historique'\n            }\n        ]\n    };\n    const stats = {\n        users: '2,500+',\n        transports: '15,000+',\n        savings: '25%',\n        satisfaction: '98%'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"text-2xl font-bold text-indigo-600\",\n                                        children: \"SABLOGISTIQUE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-4 text-gray-500\",\n                                        children: \"D\\xe9monstration interactive\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-indigo-600\",\n                                        children: \"Accueil\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/register\",\n                                        className: \"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700\",\n                                        children: \"Commencer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-extrabold text-gray-900 sm:text-5xl\",\n                                children: \"D\\xe9couvrez SABLOGISTIQUE en action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Explorez les fonctionnalit\\xe9s de notre plateforme de transport de fret et d\\xe9couvrez comment nous r\\xe9volutionnons la logistique.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-8 md:grid-cols-4 mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-indigo-600\",\n                                        children: stats.users\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Utilisateurs actifs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-indigo-600\",\n                                        children: stats.transports\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Transports r\\xe9alis\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-indigo-600\",\n                                        children: stats.savings\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"\\xc9conomies moyennes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-indigo-600\",\n                                        children: stats.satisfaction\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Satisfaction client\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-1 shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveDemo('shipper'),\n                                    className: `px-6 py-3 rounded-md font-medium transition-colors ${activeDemo === 'shipper' ? 'bg-indigo-600 text-white' : 'text-gray-600 hover:text-indigo-600'}`,\n                                    children: \"\\uD83D\\uDC64 Affr\\xe9teur (Exp\\xe9diteur)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveDemo('carrier'),\n                                    className: `px-6 py-3 rounded-md font-medium transition-colors ${activeDemo === 'carrier' ? 'bg-indigo-600 text-white' : 'text-gray-600 hover:text-indigo-600'}`,\n                                    children: \"\\uD83D\\uDE9B Transporteur\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\",\n                        children: demoFeatures[activeDemo].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-3\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-indigo-50 rounded-md p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-indigo-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"D\\xe9mo :\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \" \",\n                                                            feature.demo\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            }, index, false, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-8 mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6 text-center\",\n                                children: \"Simulation en temps r\\xe9el\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            activeDemo === 'shipper' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                children: \"\\uD83D\\uDCCB Cr\\xe9er une nouvelle offre de fret\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Origine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                                                placeholder: \"Paris, France\",\n                                                                disabled: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Destination\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                                                placeholder: \"Lyon, France\",\n                                                                disabled: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Poids (kg)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                                                placeholder: \"2500\",\n                                                                disabled: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Budget (€)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                                                placeholder: \"850\",\n                                                                disabled: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/shipper/offers/new\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        children: \"Essayer la cr\\xe9ation d'offre\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                children: \"\\uD83D\\uDCB0 Offres re\\xe7ues (simulation)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Transport Express SARL\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Livraison en 8h • Note: 4.8/5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold text-green-600\",\n                                                                children: \"780 €\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Logistique Rapide\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"Livraison en 12h • Note: 4.6/5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold text-green-600\",\n                                                                children: \"720 €\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                children: \"\\uD83D\\uDD0D Rechercher des offres de fret\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Zone g\\xe9ographique\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                                                disabled: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    children: \"\\xcele-de-France\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Type de marchandise\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                                                disabled: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    children: \"Tous types\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                children: \"Capacit\\xe9 min (kg)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md\",\n                                                                placeholder: \"1000\",\n                                                                disabled: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/carrier/offers\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        children: \"Explorer les offres disponibles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                children: \"\\uD83D\\uDCE6 Offres correspondantes (simulation)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Transport Paris → Lyon\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"2500 kg • \\xc9lectronique • 465 km\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold text-blue-600\",\n                                                                children: \"850 € max\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center p-3 bg-white rounded border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Livraison Bordeaux → Nantes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"3200 kg • Alimentaire • 347 km\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold text-blue-600\",\n                                                                children: \"720 € max\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-indigo-600 rounded-lg p-8 text-center text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold mb-4\",\n                                children: \"Pr\\xeat \\xe0 r\\xe9volutionner votre logistique ?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl mb-6 opacity-90\",\n                                children: \"Rejoignez les milliers d'entreprises qui font confiance \\xe0 SABLOGISTIQUE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/register\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"bg-white text-indigo-600 hover:bg-gray-100\",\n                                            children: \"Cr\\xe9er un compte gratuit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"border-white text-white hover:bg-indigo-700\",\n                                            children: \"Demander une d\\xe9mo personnalis\\xe9e\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\demo\\\\page.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/demo/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = ({ variant = 'primary', size = 'md', className, children, ...props })=>{\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    const variants = {\n        primary: 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500',\n        secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n        outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500',\n        ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'\n    };\n    const sizes = {\n        sm: 'h-8 px-3 text-sm',\n        md: 'h-10 px-4 text-base',\n        lg: 'h-12 px-6 text-lg'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance),\n/* harmony export */   formatWeight: () => (/* binding */ formatWeight)\n/* harmony export */ });\n// Fonction utilitaire pour combiner les classes CSS\nfunction cn(...inputs) {\n    return inputs.filter(Boolean).join(' ');\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('fr-FR', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(date);\n}\nfunction formatCurrency(amount, currency = 'EUR') {\n    return new Intl.NumberFormat('fr-FR', {\n        style: 'currency',\n        currency\n    }).format(amount);\n}\nfunction formatWeight(weight, unit = 'kg') {\n    return `${weight.toLocaleString('fr-FR')} ${unit}`;\n}\nfunction formatDistance(distance, unit = 'km') {\n    return `${distance.toLocaleString('fr-FR')} ${unit}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdemo%2Fpage&page=%2Fdemo%2Fpage&appPaths=%2Fdemo%2Fpage&pagePath=private-next-app-dir%2Fdemo%2Fpage.tsx&appDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp1%5Chtdocs%5CSABLOGISTIQUE%5Csablogistique&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();