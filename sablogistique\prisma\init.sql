-- Script d'initialisation pour SABLOGISTIQUE
-- Ce script sera exécuté automatiquement lors de la création du conteneur PostgreSQL

-- Créer la base de données si elle n'existe pas
-- (PostgreSQL crée automatiquement la DB spécifiée dans POSTGRES_DB)

-- Créer des extensions utiles
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis"; -- Pour la géolocalisation (optionnel)

-- Créer un utilisateur spécifique pour l'application (optionnel)
-- CREATE USER sablogistique WITH PASSWORD 'sablogistique123';
-- GRANT ALL PRIVILEGES ON DATABASE sablogistique TO sablogistique;

-- Message de confirmation
SELECT 'Base de données SABLOGISTIQUE initialisée avec succès!' as message;
