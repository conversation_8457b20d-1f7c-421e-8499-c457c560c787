// Fonction utilitaire pour combiner les classes CSS
export function cn(...inputs: (string | undefined | null | boolean)[]) {
  return inputs.filter(Boolean).join(' ');
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
}

export function formatCurrency(amount: number, currency: string = 'EUR'): string {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency,
  }).format(amount);
}

export function formatWeight(weight: number, unit: string = 'kg'): string {
  return `${weight.toLocaleString('fr-FR')} ${unit}`;
}

export function formatDistance(distance: number, unit: string = 'km'): string {
  return `${distance.toLocaleString('fr-FR')} ${unit}`;
}
