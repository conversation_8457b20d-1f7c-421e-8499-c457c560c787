// Types pour les utilisateurs
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'SHIPPER' | 'CARRIER' | 'ADMIN';
  company?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Types pour les offres de fret
export interface FreightOffer {
  id: string;
  title: string;
  description: string;
  type: 'SPOT' | 'TERM' | 'INTERNATIONAL';
  status: 'ACTIVE' | 'ASSIGNED' | 'COMPLETED' | 'CANCELLED';
  
  // Détails du transport
  origin: Location;
  destination: Location;
  pickupDate: Date;
  deliveryDate?: Date;
  
  // Détails de la marchandise
  cargoType: string;
  weight: number; // en kg
  volume?: number; // en m³
  quantity: number;
  specialRequirements?: string[];
  
  // Détails financiers
  budget?: number;
  currency: string;
  
  // Relations
  shipperId: string;
  carrierId?: string;
  
  createdAt: Date;
  updatedAt: Date;
}

// Types pour les localisations
export interface Location {
  id: string;
  address: string;
  city: string;
  postalCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
}

// Types pour les véhicules
export interface Vehicle {
  id: string;
  type: 'TRUCK' | 'VAN' | 'TRAILER' | 'CONTAINER';
  licensePlate: string;
  capacity: number; // en kg
  volume?: number; // en m³
  carrierId: string;
  isAvailable: boolean;
  currentLocation?: Location;
}

// Types pour le suivi
export interface TrackingEvent {
  id: string;
  freightOfferId: string;
  type: 'PICKUP' | 'IN_TRANSIT' | 'DELIVERY' | 'DELAY' | 'INCIDENT';
  description: string;
  location?: Location;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

// Types pour les enchères
export interface Bid {
  id: string;
  freightOfferId: string;
  carrierId: string;
  amount: number;
  currency: string;
  proposedPickupDate: Date;
  proposedDeliveryDate: Date;
  message?: string;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'WITHDRAWN';
  createdAt: Date;
}

// Types pour les notifications
export interface Notification {
  id: string;
  userId: string;
  type: 'BID_RECEIVED' | 'BID_ACCEPTED' | 'TRACKING_UPDATE' | 'SYSTEM';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
}

// Types pour les filtres de recherche
export interface SearchFilters {
  origin?: string;
  destination?: string;
  cargoType?: string;
  minWeight?: number;
  maxWeight?: number;
  pickupDateFrom?: Date;
  pickupDateTo?: Date;
  maxBudget?: number;
  type?: FreightOffer['type'];
}

// Types pour les statistiques
export interface DashboardStats {
  totalOffers: number;
  activeOffers: number;
  completedOffers: number;
  totalRevenue: number;
  averageRating: number;
}
