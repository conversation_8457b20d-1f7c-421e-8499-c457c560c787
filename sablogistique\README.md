# SABLOGISTIQUE - Plateforme de Transport de Fret

SABLOGISTIQUE est une plateforme en ligne moderne qui met en relation les affréteurs (expéditeurs) et les transporteurs pour optimiser le transport de marchandises. La plateforme permet de réduire les coûts de transport, d'optimiser les trajets et de diminuer les parcours à vide.

## 🚀 Fonctionnalités Principales

### Pour les Affréteurs
- **Publication d'offres de fret** : Spot, à terme, et international
- **Gestion des appels d'offres** : Système de mise en concurrence transparent
- **Suivi en temps réel** : Traçabilité complète des expéditions
- **Optimisation des coûts** : Comparaison et sélection des meilleures offres
- **Tableaux de bord** : Analytics et reporting détaillés

### Pour les Transporteurs
- **Recherche d'offres** : Filtrage avancé par critères multiples
- **Système d'enchères** : Soumission d'offres compétitives
- **Gestion de flotte** : Administration des véhicules et conducteurs
- **Optimisation des trajets** : Réduction des parcours à vide
- **Suivi des revenus** : Tableaux de bord financiers

### Fonctionnalités Communes
- **Suivi GPS en temps réel** : Géolocalisation et notifications
- **Intégration TMS** : Connectivité avec les systèmes existants
- **Communication intégrée** : Messagerie entre utilisateurs
- **Gestion documentaire** : Documents de transport et douaniers
- **Support multilingue** : Interface en français et autres langues

## 🛠️ Technologies Utilisées

### Frontend
- **Next.js 15** - Framework React avec App Router
- **TypeScript** - Typage statique pour une meilleure robustesse
- **Tailwind CSS** - Framework CSS utilitaire pour un design moderne
- **React Query** - Gestion des données et du cache côté client

### Backend
- **Next.js API Routes** - APIs REST intégrées
- **Prisma** - ORM moderne pour la gestion de base de données
- **PostgreSQL** - Base de données relationnelle robuste
- **NextAuth.js** - Authentification sécurisée multi-providers

### Outils de Développement
- **ESLint** - Linting et qualité du code
- **Prettier** - Formatage automatique du code
- **TypeScript** - Vérification de types
- **Git** - Contrôle de version

## 📦 Installation et Configuration

### Prérequis
- Node.js 18+
- PostgreSQL 14+
- npm ou yarn

### Installation

1. **Cloner le repository**
```bash
git clone https://github.com/votre-username/sablogistique.git
cd sablogistique
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration de l'environnement**
```bash
cp .env.example .env.local
```

Modifiez le fichier `.env.local` avec vos configurations :
```env
DATABASE_URL="postgresql://username:password@localhost:5432/sablogistique"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="votre-clé-secrète-très-sécurisée"
```

4. **Configuration de la base de données**
```bash
# Générer le client Prisma
npx prisma generate

# Appliquer les migrations
npx prisma db push

# (Optionnel) Peupler avec des données de test
npx prisma db seed
```

5. **Démarrer le serveur de développement**
```bash
npm run dev
```

L'application sera accessible sur [http://localhost:3000](http://localhost:3000)

## 🗃️ Structure du Projet

```
sablogistique/
├── src/
│   ├── app/                    # Pages et routes (App Router)
│   │   ├── carrier/           # Pages transporteurs
│   │   ├── shipper/           # Pages affréteurs
│   │   ├── tracking/          # Suivi des expéditions
│   │   └── api/               # Routes API
│   ├── components/            # Composants réutilisables
│   │   └── ui/                # Composants d'interface
│   ├── lib/                   # Utilitaires et configurations
│   ├── types/                 # Définitions TypeScript
│   └── styles/                # Styles globaux
├── prisma/                    # Schéma et migrations de base de données
├── public/                    # Assets statiques
└── docs/                      # Documentation
```

## 🔧 Scripts Disponibles

```bash
# Développement
npm run dev          # Démarrer le serveur de développement
npm run build        # Construire pour la production
npm run start        # Démarrer le serveur de production
npm run lint         # Vérifier la qualité du code

# Base de données
npx prisma studio    # Interface graphique pour la base de données
npx prisma generate  # Générer le client Prisma
npx prisma db push   # Appliquer les changements de schéma
npx prisma migrate   # Créer et appliquer des migrations
```

## 🚀 Déploiement

### Vercel (Recommandé)
1. Connectez votre repository GitHub à Vercel
2. Configurez les variables d'environnement
3. Déployez automatiquement à chaque push

### Docker
```bash
# Construire l'image
docker build -t sablogistique .

# Démarrer le conteneur
docker run -p 3000:3000 sablogistique
```

## 📊 Fonctionnalités Avancées

### Intégration TMS
- APIs REST pour l'intégration avec les systèmes TMS existants
- Webhooks pour les notifications en temps réel
- Format de données standardisé

### Géolocalisation et Suivi
- Intégration Google Maps / Mapbox
- Suivi GPS en temps réel
- Notifications automatiques d'événements

### Analytics et Reporting
- Tableaux de bord personnalisables
- Métriques de performance
- Rapports d'optimisation

## 🔒 Sécurité

- Authentification multi-facteurs
- Chiffrement des données sensibles
- Validation côté serveur
- Protection CSRF
- Rate limiting sur les APIs

## 🤝 Contribution

1. Fork le projet
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Committez vos changements (`git commit -m 'Ajout d'une nouvelle fonctionnalité'`)
4. Poussez vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Ouvrez une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 📞 Support

- **Documentation** : [docs.sablogistique.com](https://docs.sablogistique.com)
- **Email** : <EMAIL>
- **Issues** : [GitHub Issues](https://github.com/votre-username/sablogistique/issues)

## 🗺️ Roadmap

### Version 1.1
- [ ] Application mobile (React Native)
- [ ] Intégration IoT pour le suivi des conteneurs
- [ ] IA pour l'optimisation des routes

### Version 1.2
- [ ] Marketplace étendu (Europe)
- [ ] Système de notation et avis
- [ ] Intégration blockchain pour la traçabilité

### Version 2.0
- [ ] Plateforme multimodale (air, mer, terre)
- [ ] Prédictions basées sur l'IA
- [ ] Intégration complète avec les douanes

---

**SABLOGISTIQUE** - Révolutionner le transport de fret grâce à la technologie 🚛✨
