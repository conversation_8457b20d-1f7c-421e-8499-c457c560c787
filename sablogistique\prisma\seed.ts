import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Début du seeding de la base de données...')

  // Nettoyer les données existantes
  await prisma.notification.deleteMany()
  await prisma.trackingEvent.deleteMany()
  await prisma.bid.deleteMany()
  await prisma.freightOffer.deleteMany()
  await prisma.vehicle.deleteMany()
  await prisma.location.deleteMany()
  await prisma.user.deleteMany()

  // Créer des utilisateurs de test
  const shipper1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'SHIPPER',
      company: 'TechCorp SARL',
      phone: '+33 1 23 45 67 89',
    },
  })

  const carrier1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      role: '<PERSON><PERSON><PERSON><PERSON>',
      company: 'Transport Express',
      phone: '+33 1 98 76 54 32',
    },
  })

  const carrier2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>rand',
      role: 'CARRIER',
      company: 'Logistique Rapide',
      phone: '+33 1 11 22 33 44',
    },
  })

  // Créer des localisations
  const parisLocation = await prisma.location.create({
    data: {
      address: '123 Rue de Rivoli',
      city: 'Paris',
      postalCode: '75001',
      country: 'France',
      latitude: 48.8566,
      longitude: 2.3522,
    },
  })

  const lyonLocation = await prisma.location.create({
    data: {
      address: '456 Avenue de la République',
      city: 'Lyon',
      postalCode: '69001',
      country: 'France',
      latitude: 45.7640,
      longitude: 4.8357,
    },
  })

  const marseilleLocation = await prisma.location.create({
    data: {
      address: '789 Boulevard de la Liberté',
      city: 'Marseille',
      postalCode: '13001',
      country: 'France',
      latitude: 43.2965,
      longitude: 5.3698,
    },
  })

  // Créer des véhicules
  const vehicle1 = await prisma.vehicle.create({
    data: {
      type: 'TRUCK',
      licensePlate: 'AB-123-CD',
      capacity: 20000,
      volume: 80,
      driver: 'Michel Leblanc',
      carrierId: carrier1.id,
      currentLocationId: parisLocation.id,
      lastMaintenance: new Date('2024-01-01'),
      nextMaintenance: new Date('2024-04-01'),
    },
  })

  const vehicle2 = await prisma.vehicle.create({
    data: {
      type: 'VAN',
      licensePlate: 'EF-456-GH',
      capacity: 3500,
      volume: 15,
      driver: 'Sophie Moreau',
      carrierId: carrier2.id,
      currentLocationId: lyonLocation.id,
      lastMaintenance: new Date('2023-12-15'),
      nextMaintenance: new Date('2024-03-15'),
    },
  })

  // Créer des offres de fret
  const offer1 = await prisma.freightOffer.create({
    data: {
      title: 'Transport Paris - Lyon',
      description: 'Transport de matériel électronique fragile. Manipulation avec précaution requise.',
      type: 'SPOT',
      status: 'ACTIVE',
      cargoType: 'Électronique',
      weight: 2500,
      volume: 15.5,
      quantity: 10,
      specialRequirements: JSON.stringify(['Fragile', 'Assurance renforcée']),
      pickupDate: new Date('2024-01-15T08:00:00'),
      deliveryDate: new Date('2024-01-15T18:00:00'),
      budget: 850,
      currency: 'EUR',
      shipperId: shipper1.id,
      originId: parisLocation.id,
      destinationId: lyonLocation.id,
    },
  })

  const offer2 = await prisma.freightOffer.create({
    data: {
      title: 'Livraison Marseille - Lyon',
      description: 'Produits alimentaires frais. Transport réfrigéré nécessaire.',
      type: 'TERM',
      status: 'ACTIVE',
      cargoType: 'Alimentaire',
      weight: 3200,
      volume: 22.0,
      quantity: 50,
      specialRequirements: JSON.stringify(['Réfrigéré', 'Livraison rapide']),
      pickupDate: new Date('2024-01-18T09:00:00'),
      deliveryDate: new Date('2024-01-18T16:00:00'),
      budget: 720,
      currency: 'EUR',
      shipperId: shipper1.id,
      originId: marseilleLocation.id,
      destinationId: lyonLocation.id,
    },
  })

  // Créer des enchères
  const bid1 = await prisma.bid.create({
    data: {
      amount: 800,
      currency: 'EUR',
      proposedPickupDate: new Date('2024-01-15T08:00:00'),
      proposedDeliveryDate: new Date('2024-01-15T17:00:00'),
      message: 'Transport sécurisé avec assurance complète. Véhicule équipé pour matériel fragile.',
      status: 'PENDING',
      freightOfferId: offer1.id,
      carrierId: carrier1.id,
    },
  })

  const bid2 = await prisma.bid.create({
    data: {
      amount: 720,
      currency: 'EUR',
      proposedPickupDate: new Date('2024-01-15T09:00:00'),
      proposedDeliveryDate: new Date('2024-01-15T19:00:00'),
      message: 'Livraison express avec suivi GPS en temps réel.',
      status: 'PENDING',
      freightOfferId: offer1.id,
      carrierId: carrier2.id,
    },
  })

  // Créer des événements de suivi
  const trackingEvent1 = await prisma.trackingEvent.create({
    data: {
      type: 'PICKUP',
      description: 'Marchandise collectée à Paris',
      timestamp: new Date('2024-01-14T08:00:00'),
      freightOfferId: offer1.id,
      locationId: parisLocation.id,
      metadata: {
        driver: 'Michel Leblanc',
        vehicle: 'AB-123-CD',
        temperature: 20,
      },
    },
  })

  const trackingEvent2 = await prisma.trackingEvent.create({
    data: {
      type: 'IN_TRANSIT',
      description: 'Transport en cours vers Lyon',
      timestamp: new Date('2024-01-14T12:30:00'),
      freightOfferId: offer1.id,
      metadata: {
        currentSpeed: 90,
        estimatedArrival: '2024-01-14T18:00:00',
      },
    },
  })

  // Créer des notifications
  await prisma.notification.create({
    data: {
      type: 'BID_RECEIVED',
      title: 'Nouvelle offre reçue',
      message: 'Vous avez reçu une nouvelle offre pour votre transport Paris - Lyon',
      isRead: false,
      userId: shipper1.id,
    },
  })

  await prisma.notification.create({
    data: {
      type: 'TRACKING_UPDATE',
      title: 'Mise à jour de suivi',
      message: 'Votre marchandise est en cours de transport',
      isRead: false,
      userId: shipper1.id,
    },
  })

  console.log('✅ Seeding terminé avec succès!')
  console.log(`👤 Utilisateurs créés: ${await prisma.user.count()}`)
  console.log(`📍 Localisations créées: ${await prisma.location.count()}`)
  console.log(`🚛 Véhicules créés: ${await prisma.vehicle.count()}`)
  console.log(`📦 Offres créées: ${await prisma.freightOffer.count()}`)
  console.log(`💰 Enchères créées: ${await prisma.bid.count()}`)
  console.log(`📍 Événements de suivi: ${await prisma.trackingEvent.count()}`)
  console.log(`🔔 Notifications créées: ${await prisma.notification.count()}`)
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Erreur lors du seeding:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
