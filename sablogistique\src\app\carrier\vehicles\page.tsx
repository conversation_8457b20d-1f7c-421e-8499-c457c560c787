'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

// Mock data pour la démonstration
const mockVehicles = [
  {
    id: '1',
    type: 'TRUCK',
    licensePlate: 'AB-123-CD',
    capacity: 20000,
    volume: 80,
    isAvailable: true,
    currentLocation: 'Paris, France',
    lastMaintenance: '2024-01-01',
    nextMaintenance: '2024-04-01',
    driver: '<PERSON>',
    status: 'AVAILABLE',
  },
  {
    id: '2',
    type: 'VAN',
    licensePlate: 'EF-456-GH',
    capacity: 3500,
    volume: 15,
    isAvailable: false,
    currentLocation: 'Lyon, France',
    lastMaintenance: '2023-12-15',
    nextMaintenance: '2024-03-15',
    driver: '<PERSON>',
    status: 'IN_TRANSIT',
  },
  {
    id: '3',
    type: 'TRAILER',
    licensePlate: 'IJ-789-KL',
    capacity: 40000,
    volume: 120,
    isAvailable: true,
    currentLocation: 'Marseille, France',
    lastMaintenance: '2024-01-10',
    nextMaintenance: '2024-04-10',
    driver: '<PERSON>',
    status: 'MAINTENANCE',
  },
];

export default function VehiclesPage() {
  const [vehicles, setVehicles] = useState(mockVehicles);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newVehicle, setNewVehicle] = useState({
    type: 'TRUCK',
    licensePlate: '',
    capacity: '',
    volume: '',
    driver: '',
  });

  const getVehicleTypeText = (type: string) => {
    switch (type) {
      case 'TRUCK':
        return 'Camion';
      case 'VAN':
        return 'Fourgon';
      case 'TRAILER':
        return 'Semi-remorque';
      case 'CONTAINER':
        return 'Conteneur';
      default:
        return type;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800';
      case 'IN_TRANSIT':
        return 'bg-blue-100 text-blue-800';
      case 'MAINTENANCE':
        return 'bg-yellow-100 text-yellow-800';
      case 'UNAVAILABLE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'Disponible';
      case 'IN_TRANSIT':
        return 'En transport';
      case 'MAINTENANCE':
        return 'Maintenance';
      case 'UNAVAILABLE':
        return 'Indisponible';
      default:
        return status;
    }
  };

  const handleAddVehicle = (e: React.FormEvent) => {
    e.preventDefault();
    const vehicle = {
      id: Date.now().toString(),
      ...newVehicle,
      capacity: parseInt(newVehicle.capacity),
      volume: parseFloat(newVehicle.volume),
      isAvailable: true,
      currentLocation: 'À définir',
      lastMaintenance: new Date().toISOString().split('T')[0],
      nextMaintenance: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'AVAILABLE',
    };
    
    setVehicles([...vehicles, vehicle]);
    setShowAddModal(false);
    setNewVehicle({
      type: 'TRUCK',
      licensePlate: '',
      capacity: '',
      volume: '',
      driver: '',
    });
  };

  const toggleVehicleStatus = (vehicleId: string) => {
    setVehicles(vehicles.map(vehicle => 
      vehicle.id === vehicleId 
        ? { 
            ...vehicle, 
            isAvailable: !vehicle.isAvailable,
            status: vehicle.isAvailable ? 'UNAVAILABLE' : 'AVAILABLE'
          }
        : vehicle
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-indigo-600">
                SABLOGISTIQUE
              </Link>
              <span className="ml-4 text-gray-500">Gestion des véhicules</span>
            </div>
            <nav className="flex space-x-8">
              <Link href="/carrier" className="text-gray-600 hover:text-indigo-600">
                Tableau de bord
              </Link>
              <Link href="/carrier/offers" className="text-gray-600 hover:text-indigo-600">
                Offres disponibles
              </Link>
              <Button variant="outline" size="sm">
                Déconnexion
              </Button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header with Add Button */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Mes véhicules</h1>
            <p className="text-gray-600">Gérez votre flotte de véhicules</p>
          </div>
          <Button onClick={() => setShowAddModal(true)}>
            Ajouter un véhicule
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total véhicules</dt>
                    <dd className="text-lg font-medium text-gray-900">{vehicles.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Disponibles</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {vehicles.filter(v => v.status === 'AVAILABLE').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">En transport</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {vehicles.filter(v => v.status === 'IN_TRANSIT').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Maintenance</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {vehicles.filter(v => v.status === 'MAINTENANCE').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Vehicles Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {vehicles.map((vehicle) => (
            <div key={vehicle.id} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-indigo-500 rounded-md flex items-center justify-center">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">{vehicle.licensePlate}</h3>
                      <p className="text-sm text-gray-500">{getVehicleTypeText(vehicle.type)}</p>
                    </div>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(vehicle.status)}`}>
                    {getStatusText(vehicle.status)}
                  </span>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Capacité:</span>
                    <span className="text-sm font-medium text-gray-900">{vehicle.capacity.toLocaleString()} kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Volume:</span>
                    <span className="text-sm font-medium text-gray-900">{vehicle.volume} m³</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Conducteur:</span>
                    <span className="text-sm font-medium text-gray-900">{vehicle.driver}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Position:</span>
                    <span className="text-sm font-medium text-gray-900">{vehicle.currentLocation}</span>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between text-xs text-gray-500 mb-2">
                    <span>Dernière maintenance: {new Date(vehicle.lastMaintenance).toLocaleDateString('fr-FR')}</span>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mb-4">
                    <span>Prochaine maintenance: {new Date(vehicle.nextMaintenance).toLocaleDateString('fr-FR')}</span>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant={vehicle.isAvailable ? "outline" : "primary"}
                      onClick={() => toggleVehicleStatus(vehicle.id)}
                      className="flex-1"
                    >
                      {vehicle.isAvailable ? 'Désactiver' : 'Activer'}
                    </Button>
                    <Button size="sm" variant="outline">
                      Modifier
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Add Vehicle Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Ajouter un nouveau véhicule
              </h3>
              <form onSubmit={handleAddVehicle} className="space-y-4">
                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                    Type de véhicule *
                  </label>
                  <select
                    id="type"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    value={newVehicle.type}
                    onChange={(e) => setNewVehicle(prev => ({ ...prev, type: e.target.value }))}
                  >
                    <option value="TRUCK">Camion</option>
                    <option value="VAN">Fourgon</option>
                    <option value="TRAILER">Semi-remorque</option>
                    <option value="CONTAINER">Conteneur</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="licensePlate" className="block text-sm font-medium text-gray-700">
                    Plaque d&apos;immatriculation *
                  </label>
                  <input
                    type="text"
                    id="licensePlate"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="AB-123-CD"
                    value={newVehicle.licensePlate}
                    onChange={(e) => setNewVehicle(prev => ({ ...prev, licensePlate: e.target.value }))}
                  />
                </div>

                <div>
                  <label htmlFor="capacity" className="block text-sm font-medium text-gray-700">
                    Capacité (kg) *
                  </label>
                  <input
                    type="number"
                    id="capacity"
                    required
                    min="1"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="20000"
                    value={newVehicle.capacity}
                    onChange={(e) => setNewVehicle(prev => ({ ...prev, capacity: e.target.value }))}
                  />
                </div>

                <div>
                  <label htmlFor="volume" className="block text-sm font-medium text-gray-700">
                    Volume (m³) *
                  </label>
                  <input
                    type="number"
                    id="volume"
                    required
                    min="0.1"
                    step="0.1"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="80"
                    value={newVehicle.volume}
                    onChange={(e) => setNewVehicle(prev => ({ ...prev, volume: e.target.value }))}
                  />
                </div>

                <div>
                  <label htmlFor="driver" className="block text-sm font-medium text-gray-700">
                    Conducteur *
                  </label>
                  <input
                    type="text"
                    id="driver"
                    required
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Jean Dupont"
                    value={newVehicle.driver}
                    onChange={(e) => setNewVehicle(prev => ({ ...prev, driver: e.target.value }))}
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddModal(false)}
                    className="flex-1"
                  >
                    Annuler
                  </Button>
                  <Button type="submit" className="flex-1">
                    Ajouter
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
