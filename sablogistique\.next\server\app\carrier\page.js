(()=>{var e={};e.id=245,e.ids=[245],e.modules={440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1869:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(5239),i=r(8088),a=r(8170),n=r.n(a),l=r(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o={children:["",{children:["carrier",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4063)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\carrier\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\carrier\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/carrier/page",pathname:"/carrier",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3557:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var t=r(687),i=r(3210),a=r(5814),n=r.n(a),l=r(7320);let d=[{id:"1",title:"Transport Paris - Lyon",type:"SPOT",origin:"Paris, France",destination:"Lyon, France",pickupDate:"2024-01-15",weight:2500,cargoType:"\xc9lectronique",budget:850,distance:465},{id:"2",title:"Livraison Bordeaux - Nantes",type:"TERM",origin:"Bordeaux, France",destination:"Nantes, France",pickupDate:"2024-01-18",weight:3200,cargoType:"Alimentaire",budget:720,distance:347}],o=[{id:"1",offerId:"1",title:"Transport Paris - Lyon",bidAmount:800,status:"PENDING",submittedAt:"2024-01-10"},{id:"2",offerId:"3",title:"Livraison Marseille - Nice",bidAmount:450,status:"ACCEPTED",submittedAt:"2024-01-08"}],c={availableOffers:24,myBids:8,acceptedJobs:3,totalEarnings:12450};function x(){let[e,s]=(0,i.useState)("overview");return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n(),{href:"/",className:"text-2xl font-bold text-indigo-600",children:"SABLOGISTIQUE"}),(0,t.jsx)("span",{className:"ml-4 text-gray-500",children:"Tableau de bord Transporteur"})]}),(0,t.jsxs)("nav",{className:"flex space-x-8",children:[(0,t.jsx)(n(),{href:"/carrier/offers",className:"text-gray-600 hover:text-indigo-600",children:"Offres disponibles"}),(0,t.jsx)(n(),{href:"/carrier/tracking",className:"text-gray-600 hover:text-indigo-600",children:"Mes transports"}),(0,t.jsx)(n(),{href:"/carrier/vehicles",className:"text-gray-600 hover:text-indigo-600",children:"Mes v\xe9hicules"}),(0,t.jsx)(n(),{href:"/profile",className:"text-gray-600 hover:text-indigo-600",children:"Profil"}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",children:"D\xe9connexion"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)("div",{className:"border-b border-gray-200 mb-8",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsx)("button",{onClick:()=>s("overview"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"overview"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Vue d'ensemble"}),(0,t.jsx)("button",{onClick:()=>s("offers"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"offers"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Offres disponibles"}),(0,t.jsx)("button",{onClick:()=>s("bids"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"bids"===e?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Mes offres"})]})}),"overview"===e&&(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Offres disponibles"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.availableOffers})]})})]})})}),(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Mes offres"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.myBids})]})})]})})}),(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Transports accept\xe9s"}),(0,t.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:c.acceptedJobs})]})})]})})}),(0,t.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Revenus totaux"}),(0,t.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[c.totalEarnings.toLocaleString()," €"]})]})})]})})})]}),(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Actions rapides"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,t.jsx)(n(),{href:"/carrier/offers",children:(0,t.jsx)(l.$,{className:"w-full",children:"Parcourir les offres"})}),(0,t.jsx)(n(),{href:"/carrier/vehicles",children:(0,t.jsx)(l.$,{variant:"outline",className:"w-full",children:"G\xe9rer mes v\xe9hicules"})}),(0,t.jsx)(n(),{href:"/carrier/tracking",children:(0,t.jsx)(l.$,{variant:"outline",className:"w-full",children:"Suivre mes transports"})})]})]})]}),"offers"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Offres de fret disponibles"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(l.$,{variant:"outline",size:"sm",children:"Filtrer"}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",children:"Trier"})]})]}),(0,t.jsx)("div",{className:"grid gap-6 lg:grid-cols-2",children:d.map(e=>(0,t.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.title}),(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.type})]}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Trajet:"})," ",e.origin," → ",e.destination]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Date de collecte:"})," ",new Date(e.pickupDate).toLocaleDateString("fr-FR")]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Marchandise:"})," ",e.cargoType," • ",e.weight," kg"]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Distance:"})," ",e.distance," km"]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:[e.budget," €"]}),(0,t.jsx)(l.$,{size:"sm",children:"Faire une offre"})]})]},e.id))})]}),"bids"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Mes offres soumises"}),(0,t.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,t.jsx)("ul",{className:"divide-y divide-gray-200",children:o.map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)("div",{className:"px-4 py-4 sm:px-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-indigo-600 truncate",children:e.title}),(0,t.jsx)("span",{className:`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"ACCEPTED"===e.status?"bg-green-100 text-green-800":"PENDING"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:"ACCEPTED"===e.status?"Accept\xe9e":"PENDING"===e.status?"En attente":"Refus\xe9e"})]}),(0,t.jsx)("div",{className:"ml-2 flex-shrink-0",children:(0,t.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[e.bidAmount," €"]})})]}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Soumise le ",new Date(e.submittedAt).toLocaleDateString("fr-FR")]})})]})},e.id))})})]})]})]})}},3852:()=>{},3873:e=>{"use strict";e.exports=require("path")},4063:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\xampp1\\\\htdocs\\\\SABLOGISTIQUE\\\\sablogistique\\\\src\\\\app\\\\carrier\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\xampp1\\htdocs\\SABLOGISTIQUE\\sablogistique\\src\\app\\carrier\\page.tsx","default")},4431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o,metadata:()=>d});var t=r(7413),i=r(2376),a=r.n(i),n=r(8726),l=r.n(n);r(1135);let d={title:"Create Next App",description:"Generated by create next app"};function o({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:`${a().variable} ${l().variable} antialiased`,children:e})})}},4543:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4597:(e,s,r)=>{Promise.resolve().then(r.bind(r,4063))},5708:()=>{},7320:(e,s,r)=>{"use strict";r.d(s,{$:()=>i});var t=r(687);r(3210);let i=({variant:e="primary",size:s="md",className:r,children:i,...a})=>(0,t.jsx)("button",{className:function(...e){return e.filter(Boolean).join(" ")}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[e],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-base",lg:"h-12 px-6 text-lg"}[s],r),...a,children:i})},8834:(e,s,r)=>{Promise.resolve().then(r.bind(r,3557))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9815:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,145,567],()=>r(1869));module.exports=t})();